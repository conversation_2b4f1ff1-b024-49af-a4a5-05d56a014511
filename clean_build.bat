@echo off
echo Cleaning build directories...

REM Kill any running Java processes
taskkill /F /IM java.exe 2>nul
taskkill /F /IM adb.exe 2>nul
taskkill /F /IM gradle.exe 2>nul

REM Wait a moment for processes to terminate
timeout /t 3 /nobreak >nul

REM Create empty temp directory for robocopy trick
if not exist "temp_empty" mkdir "temp_empty"

REM Use robocopy to delete directories with long paths (more aggressive)
if exist "app\build" (
    echo Removing app\build directory...
    robocopy "temp_empty" "app\build" /MIR /NFL /NDL /NJH /NJS /nc /ns /np >nul 2>&1
    rmdir /s /q "app\build" >nul 2>&1
)

if exist "build" (
    echo Removing root build directory...
    robocopy "temp_empty" "build" /MIR /NFL /NDL /NJH /NJS /nc /ns /np >nul 2>&1
    rmdir /s /q "build" >nul 2>&1
)

REM Clean gradle cache
if exist ".gradle" (
    echo Cleaning gradle cache...
    robocopy "temp_empty" ".gradle" /MIR /NFL /NDL /NJH /NJS /nc /ns /np >nul 2>&1
    rmdir /s /q ".gradle" >nul 2>&1
)

REM Clean up temp directory
rmdir /s /q "temp_empty" >nul 2>&1

echo Build cleanup completed!
echo You can now run: gradlew clean build
