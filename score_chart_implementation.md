# Score Chart Implementation (Deep Dive)

This document explains, in detail, how the Score Chart is implemented in this project. It covers data preparation, rendering, sizing, axis labeling, scrolling, colors, transparency, and related utilities so you can understand every component and constraint that affects the final UI.

Repositories and files referenced:
- Android UI (View): `uhabits-android/src/main/java/org/isoron/uhabits/activities/common/views/ScoreChart.kt`
- Scroll base class: `uhabits-android/src/main/java/org/isoron/uhabits/activities/common/views/ScrollableChart.kt`
- Presenter and state: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/ui/screens/habits/show/views/ScoreCard.kt`
- Data model: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/models/Score.kt`, `Timestamp.kt`
- Date/time utilities: `uhabits-core/src/jvmMain/java/org/isoron/uhabits/core/utils/DateUtils.kt`
- Android resources: `uhabits-android/src/main/res/values/dimens.xml`, `colors.xml`, theme attributes (via `StyledResources`).


## 1) Data pipeline feeding the chart

- The on-screen "Score" card is orchestrated by `ScoreCardPresenter` and `ScoreCardState`:
  - Source: `uhabits-core/.../ScoreCard.kt`.
  - The presenter exposes five bucket sizes (in days): `[1, 7, 31, 92, 365]`.
  - The presenter builds a list of `Score` objects aggregated per selected bucket:
    - It fetches the range from the oldest known entry to today.
    - It calls `habit.scores.getByInterval(oldest, today)`, then groups results by a time-truncated key computed with `DateUtils.truncate(getTruncateField(bucketSize), timestamp, firstWeekday)`.
    - For each group, the average of `Score.value` is taken and a new `Score(timestamp=groupKeyStart, value=average)` is created.
    - The resulting list is sorted by `timestamp` ascending and then reversed, so index 0 is the most recent bucket.
  - `Score` model represents a point in time with a `value` in range [0.0, 1.0].

- The view wrapper `ScoreCardView` sets up the `ScoreChart` as follows:
  - `setScores(state.scores)`
  - `reset()` to reset internal scroller/offset
  - `setBucketSize(state.bucketSize)`
  - `setColor(themeColor)` derived from the theme and habit color.


## 2) ScoreChart: high-level rendering flow

- Core file: `ScoreChart.kt`
- The class extends `ScrollableChart` (see section 6).
- Key members:
  - Paints: `pText` (text), `pGrid` (grid), `pGraph` (graph/markers/lines)
  - Sizing: `baseSize`, `columnWidth` (float), `columnHeight`, `nColumns`, `internalPaddingTop`
  - Colors: `textColor`, `gridColor`, `primaryColor`, `internalBackgroundColor`
  - Date formats: `dfDay` ("d"), `dfMonth` ("MMM"), `dfYear` ("yyyy") using locale-sensitive skeletons
  - Data: `scores: List<Score>`, `bucketSize` (days), `dataOffset` (inherited from `ScrollableChart`)
  - Transparency/caching: `isTransparencyEnabled`, `internalDrawingCache`, `cacheCanvas`

- onDraw(Canvas):
  1. Choose the target canvas:
     - If transparency is enabled, draw into an offscreen ARGB bitmap `internalDrawingCache` through `cacheCanvas`; otherwise, draw directly on the provided `canvas`.
  2. Compute the main grid rectangle spanning the visible data:
     - `rect.set(0, 0, nColumns * columnWidth, columnHeight)` and then `rect.offset(0, internalPaddingTop)`.
  3. Draw grid + Y-axis labels via `drawGrid(activeCanvas, rect)` (details in section 4).
  4. Set paint colors: `pText.color = textColor`, `pGraph.color = primaryColor`.
  5. Iterate visible columns `k in 0 until nColumns`:
     - Compute the data index: `offset = nColumns - k - 1 + dataOffset`.
       - With the data list reversed by the presenter, `scores[0]` is the most recent bucket.
       - The rightmost column corresponds to `offset == dataOffset`.
       - Larger `offset` means older buckets.
       - If `offset >= scores.size`, skip drawing that column.
     - Determine the vertical position of the marker using the bucket score:
       - `height = (columnHeight * score).toInt()`; higher score plots higher.
       - Create a square `rect` of size `baseSize` and offset it to center around `(x = k*columnWidth + (columnWidth - baseSize)/2, y = internalPaddingTop + columnHeight - height - baseSize/2)`.
     - Draw a connecting line from the previous marker to the current (`drawLine`), and draw the marker itself (`drawMarker`). The last visible column also draws its marker.
     - For the footer area, reset `rect` to the full column rectangle `[x = k*columnWidth, y = internalPaddingTop, width = columnWidth, height = columnHeight]` and call `drawFooter(activeCanvas, rect, timestamp)` to render X-axis labels (details in section 5).
  6. If using the offscreen cache (transparency), composite the bitmap onto the real canvas at the end.


## 3) Measurements, dimensions, and typography

- onMeasure(widthSpec, heightSpec): sets measured size to the given specs.

- onSizeChanged(width, height, ...): computes all visual metrics from the final size.
  - Text size for labels: `pText.textSize = min(height * 0.06f, tinyTextSize)` where `tinyTextSize` is 10sp (`R.dimen.tinyTextSize`).
  - `em = pText.fontSpacing` is used as a typographic unit.
  - Footer height: `footerHeight = (3 * em).toInt()`.
  - Top internal padding: `internalPaddingTop = em.toInt()`.
  - Vertical data area:
    - `baseSize = (height - footerHeight - internalPaddingTop) / 8`
    - `columnHeight = 8 * baseSize`
  - Column width:
    - Start with `columnWidth = baseSize`.
    - Ensure text fits: `columnWidth = max(columnWidth, 1.5 * maxDayWidth)` and `columnWidth = max(columnWidth, 1.2 * maxMonthWidth)`.
      - `maxDayWidth`/`maxMonthWidth` are computed by formatting dates across a range and measuring text with `pText.measureText(...)` (see note below).
    - Number of visible columns: `nColumns = floor(width / columnWidth)`.
    - Stretch columns to fit the width exactly: `columnWidth = width / nColumns`.
    - Update `ScrollableChart` scroller bucket size: `setScrollerBucketSize(columnWidth.toInt())` so one scroll "bucket" equals one column.
  - Stroke widths:
    - `pGraph.textSize = baseSize * 0.5f`
    - `pGraph.strokeWidth = baseSize * 0.1f`
    - Grid line thickness: `pGrid.strokeWidth = min(dpToPixels(1f), baseSize * 0.05f)`

Note on `maxDayWidth` and `maxMonthWidth`:
- `maxMonthWidth` iterates months 0..11, measuring `dfMonth.format(day)`; this sets a lower bound on column width for month labels.
- `maxDayWidth` iterates day-of-month values; it formats with `dfMonth` as well (likely intended to be `dfDay`). The chart still ensures columns are wide enough for labels by considering both constraints; however, the day-measurement appears to use month format. This does not break rendering but is worth noting.


## 4) Y-axis (grid and labels)

- Drawn by `drawGrid(canvas, rGrid)` using a five-row grid:
  - `nRows = 5`, `rowHeight = rGrid.height() / nRows`.
  - For each row `i in 0 until nRows`:
    - Draw the percentage label at the top-left of the current row: `"${100 - i * 100 / nRows}%"`, i.e. `100%`, `80%`, `60%`, `40%`, `20%`.
      - Text alignment: `pText.textAlign = LEFT`.
      - Position: `x = rGrid.left + 0.5 * em`, `y = rGrid.top + 1.0 * em`.
    - Draw a horizontal grid line at the top edge of the row: `(x1 = rGrid.left, y = rGrid.top) -> (x2 = rGrid.right, y = rGrid.top)`.
    - Move `rGrid` down by `rowHeight` to process the next row.
  - After the loop, an additional final horizontal line is drawn at the bottom of the grid.
- Colors:
  - Label color: `pText.color = textColor` (`R.attr.contrast60` via `StyledResources`).
  - Grid line color: `pGrid.color = gridColor` (`R.attr.contrast20`).
- There is no explicit `0%` text label; only the bottom grid line is drawn at the 0% position.


## 5) X-axis (footer labels)

- Rendered by `drawFooter(canvas, rect, currentDate)` for each visible column (bucket).
- Date formats:
  - `dfYear = "yyyy"` (e.g., 2024)
  - `dfMonth = "MMM"` (localized abbreviated month, e.g., Jan, Feb)
  - `dfDay = "d"` (day of month without leading zero)
  - Formats are created using `toSimpleDataFormat()`, which respects device locale.

- Text alignment and positions (all centered horizontally at the column center):
  - Year label y-position: `rect.bottom + 2.2 * em`
  - Month/day label y-position: `rect.bottom + 1.2 * em`

- Label content rules:
  - Year printing rules:
    - Only print when the year changes compared to the previously printed year.
    - Additionally, when `bucketSize >= 365` (i.e., yearly or larger), it prints only on even years: odd years are intentionally skipped to reduce clutter.
    - After printing a year, `skipYear = 1` prevents printing a year in the immediately following column (avoids overlapping labels if multiple columns share the same year).
  - Month/day printing rules (applied only when `bucketSize < 365`):
    - If the month changed since the previous column, print the month abbreviation (e.g., "Jan").
    - Otherwise, print the day of month (e.g., "15").

- State tracked while iterating columns:
  - `previousYearText` and `previousMonthText` remember which labels were used last so duplicates can be avoided.


## 6) Scrolling and interactions

- `ScoreChart` inherits from `ScrollableChart`, which encapsulates all gesture and scrolling state. Important pieces:
  - Data window position: `dataOffset` (integer) – how many columns to skip from the most recent data. Rightmost column uses `offset = dataOffset`.
  - Scroller units: `scrollerBucketSize` – set equal to the computed `columnWidth` so that moving one bucket horizontally corresponds to one column of data.
  - Gesture handling:
    - `onScroll(e1, e2, dx, dy)`: updates the internal `Scroller` by `dx` scaled by direction (`±1`, default `1`). Horizontal gestures request that parents don’t intercept touch events if the horizontal intent is clear.
    - `onFling(...)`: applies inertia using `Scroller.fling` and a `ValueAnimator` to keep invalidating while movement continues; `onAnimationUpdate` calls `updateDataOffset()` until the scroller stops.
    - `onTouchEvent(event)`: forwards to an internal `GestureDetector`.
  - Persistence:
    - `onSaveInstanceState` and `onRestoreInstanceState` preserve scroller position, direction and `dataOffset`.
  - Programmatic controls:
    - `setScrollDirection(+1 or -1)` to flip direction.
    - `setMaxDataOffset(n)` to clamp the maximum offset.
    - `reset()` to return to `dataOffset = 0`.
  - Offset calculation:
    - `dataOffset = scroller.currX / scrollerBucketSize`, clamped to `[0, maxDataOffset]`.

- How the chart uses `dataOffset`:
  - For column index `k` in `[0, nColumns)`, `offset = nColumns - k - 1 + dataOffset`.
  - Given the presenter reverses data (most recent at index 0), the rightmost column shows `scores[dataOffset]` (most recent if `dataOffset == 0`). Columns to the left show older buckets.
  - If `offset >= scores.size`, that column is empty.


## 7) Plotting the series (lines + markers)

- Vertical mapping:
  - For a score in [0.0, 1.0], the pixel height is `height = columnHeight * score`.
  - The marker’s center is placed at y `= internalPaddingTop + (columnHeight - height)`.

- Connecting line (`drawLine`):
  - Straight line from center of previous marker to center of current marker using `pGraph` with `strokeWidth = baseSize * 0.1f` and `color = primaryColor`.

- Marker (`drawMarker`):
  - Start with the square `baseSize x baseSize` rectangle centered at the target location.
  - Inset by `0.225 * baseSize` and draw an oval:
    - If transparency is enabled: use `PorterDuffXfermode(CLEAR)` to punch a hole in the offscreen bitmap (revealing background below), otherwise set paint color to `internalBackgroundColor` and fill.
  - Inset again by `0.1 * baseSize` and draw a smaller oval:
    - If transparency is enabled: switch back to `XFERMODE_SRC`, otherwise set paint color to `primaryColor`.
  - Result: a colored disc with a halo/ring effect against the background; in transparent mode, the outer halo is a genuine hole (clear) in the layer.

- z-order:
  - For each point: draw connecting line first (using the `prevRect`), then draw the marker for the previous point; on the final column, also draw the current marker.


## 8) Colors, theming, and transparency

- Colors are resolved via `StyledResources(context)`:
  - `textColor = attr:contrast60`
  - `gridColor = attr:contrast20`
  - `internalBackgroundColor = attr:cardBgColor`
  - `primaryColor` is set externally by the card using the theme’s habit color; default is `Color.BLACK` if not set.

- Transparency mode (`setIsTransparencyEnabled(true)`):
  - Rendering happens on an ARGB offscreen bitmap (see `initCache(width, height)`), then the result is copied to the real canvas at the end.
  - CLEAR/SRC xfermodes are used when drawing markers to achieve the cutout/overlay effect.


## 9) Internationalization of labels

- Month/day/year formats are built using `toSimpleDataFormat()` which uses Android’s `DateFormat.getBestDateTimePattern` with device locale to derive the best localized pattern for skeletons:
  - `"MMM"` — localized abbreviated month name
  - `"yyyy"` — year
  - `"d"` — day of month
- `Calendar` values are `GMT`-based for consistency with other date utilities in the core module.


## 10) Resource dimensions and constants

- From `uhabits-android/src/main/res/values/dimens.xml`:
  - `tinyTextSize = 10sp` (upper bound for Y and X axis text size)
  - The code also computes text size at up to `6%` of view height and uses the minimum of both.

- Grid stroke minimum: `1dp` (capped with `baseSize * 0.05f`).

- Vertical layout metrics derived from font spacing `em`:
  - Top padding: `1 * em`
  - Footer total reserved height: `3 * em` (supports one row of month/day labels at `1.2 * em` and a second row of year at `2.2 * em`).

- Data area height: `8 * baseSize`.

- Column width constraints include measured text widths (see section 3) to avoid label clipping.


## 11) Edge cases and noteworthy behaviors

- X-axis year printing with yearly buckets (`bucketSize >= 365`): years are printed only on even years; odd years are skipped to reduce clutter.
- X-axis month/day printing only occurs when `bucketSize < 365`.
- Y-axis labels include `100%` down to `20%` (top to near-bottom). The baseline `0%` is represented by a grid line but has no explicit numeric label.
- Column width constraints include a potential minor inconsistency: `maxDayWidth` measures `dfMonth` text across different days, likely intended to measure `dfDay` widths; columns are still wide enough due to the additional `maxMonthWidth` constraint.
- `setMaxDataOffset` exists in `ScrollableChart`, but `ScoreCardView` does not currently set it to `scores.size - 1`; the default max is `10000`. If scrolled beyond available data, columns are left empty.
- Touch direction and `dataOffset`: with default `direction = 1`, negative `dx` in `onScroll` moves the data window towards older entries (as used in tests).


## 12) Tests and visual references

- Android instrumentation tests: `ScoreChartTest` exercises multiple cases and snapshots:
  - Base render: `render.png`
  - With data offset (scrolled): `renderDataOffset.png` (applies a negative `dx` to simulate scrolling)
  - Different size: `renderDifferentSize.png`
  - Monthly bucket: `renderMonthly.png`
  - Yearly bucket: `renderYearly.png`
  - Transparent background: `renderTransparent.png`
- These assets allow pixel-accurate regression checks of chart rendering.


## 13) Summary of core formulas and mappings

- Vertical position: `y_center = internalPaddingTop + (columnHeight - baseSize/2) - (columnHeight * score)`
- Column k → data index: `offset = (nColumns - k - 1) + dataOffset` (skip drawing if `offset >= scores.size`).
- Column width selection:
  - Start: `baseSize`
  - Ensure text fits: `>= 1.5 * maxDayWidth`, `>= 1.2 * maxMonthWidth`
  - `nColumns = floor(width / columnWidth)`, then `columnWidth = width / nColumns`
- Footer label vertical positions:
  - Month/day at `rect.bottom + 1.2 * em`
  - Year at `rect.bottom + 2.2 * em`
- Grid rows: 5 rows, labels `100%, 80%, 60%, 40%, 20%` at row tops; bottom line marks 0%.


## 14) Extension points and suggested improvements

- Use `setMaxDataOffset(scores.size - 1)` when scores are set to prevent scrolling into empty space.
- Consider correcting `maxDayWidth` to measure `dfDay` text across the widest expected day labels, to better reflect day-of-month widths.
- Optionally print the `0%` label at the bottom to match the grid line for completeness.
- If desired, expose a hook to customize the number of grid rows and labeling granularity.
- Optionally add an accessibility content description per column including date range and score percentage.


## 15) Related components (context)

- `ScrollableChart` centralizes gesture, fling, and offset logic; provides state save/restore and a bucket-sized scroller that the chart ties to `columnWidth`.
- `ScoreCardPresenter` determines the bucket boundaries and aggregation using `DateUtils.truncate(...)` with respect to `firstWeekday` and locale/timezone utilities from the core module.
- `StyledResources` supplies theme-aware colors (`contrastXX` attributes, `cardBgColor`) so the chart follows current app theme and widget transparency settings.

This concludes the full implementation breakdown of the Score Chart.
