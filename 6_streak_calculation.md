Please refer this document as a guideline for calculating the streaks and percentage calculation . 


### **Our Integrated Plan of Attack**





#### **2. Streak Calculation**
.

* **The New Logic:** A streak will be defined as consecutive **scheduled** days that have been successfully completed. The calculation will work like this:
    1.  Start from "today" and go backward.
    2.  Check if the habit was **completed** today. If not, the streak is 0.
    3.  If it was completed, check if it was **scheduled** for today. If not, the streak is still 0.
    4.  If it was completed AND scheduled, the streak is 1. Now, move to "yesterday."
    5.  Repeat the process, only counting days where the habit was both **scheduled and completed**. The streak breaks as soon as we find a scheduled day that was missed.

#### **3. "This Week's Percentage" Calculation**

This is the final piece of logic, also for **Phase 4**.

* **The New Logic:** The weekly percentage will be calculated based on **opportunities**, not just days.
    * `Total Completions` = The number of times a habit was successfully completed this week.
    * `Total Opportunities` = The number of times a habit was **scheduled** to occur this week.
    * `Percentage = (Total Completions / Total Opportunities) * 100`

This ensures that if a habit is only scheduled for 3 days a week, the calculation is based on those 3 opportunities, not all 7 days.

By integrating these solutions into our existing roadmap, we have a clear, step-by-step path to build this complex feature correctly and robustly. 

---

**Note** : Please Take the above calculations as a guideline and if needed we can change the logic as per the requirement.

---