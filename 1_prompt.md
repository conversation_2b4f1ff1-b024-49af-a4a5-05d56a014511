# Prompt 5.6: Replace History Chart with Reference Score Chart

## A. The Objective & Context

The goal of this task is to replace the current "Completion History" bar chart on the **Habit Details** screen with the superior **Score Chart** implementation from the reference project. The new chart, as seen in `68.jpg`, is a line graph that provides a clearer view of a habit's performance over time.

This task involves removing the old chart and integrating the new one, ensuring it is visually identical to the reference and correctly connected to the existing data logic.

**CRITICAL GUIDANCE**: The reference project (`uhabits-dev`) contains the exact implementation of the `ScoreChart` we want to use.
1.  Your **first step** is to locate the `ScoreCardView.kt` and its corresponding layout file in the reference project.
2.  You must **replicate this component** in our current project. This includes its custom view logic, XML layout, and any associated styling.
3.  If the reference implementation cannot be adapted directly, you are authorized to build a new component, but it **must be a pixel-perfect match** of the one in `68.jpg`.

## B. Detailed Implementation Plan

### 1. Remove the Existing "Completion History" Chart
- From the `ShowHabitActivity` layout, completely remove the `CardView` and all elements related to the "Completion History" bar chart.

### 2. Implement the "Score" Chart Card
- Create a new `CardView` in the layout to hold the Score Chart. It should have a title `TextView` with the text "Score" and a `Spinner` for filtering.
- **Filter**: The `Spinner` must contain the options: **"Week", "Month", "Quarter",** and **"Year."**
- **Chart View**:
    - Add the `ScoreChart` custom view (replicated from the reference project) to the card.
    - The chart must be a **line graph** that plots the habit's score over time. The Y-axis should range from 0% to 100%. The X-axis will display date labels (e.g., "Jun", "Jul", "Aug").
    - The line, points, and grid must be styled to match the visual appearance in `68.jpg`.

### 3. Connect the Chart to the ViewModel
- The data logic for this chart is already complete and available from the `ViewModel`.
- When a user selects a time period from the `Spinner` (e.g., "Month"), you must call the appropriate data-fetching function in the `ViewModel`.
- The data returned from the `ViewModel` must be passed to the `ScoreChart` view to be rendered.
- The chart must dynamically update every time the filter is changed.

## C. Meticulous Verification Plan

1.  **Visual Verification**:
    - **CRITICAL**: Open the details screen for a habit. Verify that the old bar chart is gone and the new "Score" line chart is displayed, matching the appearance in `68.jpg`.
    - Confirm the card has the title "Score" and a filter `Spinner` on the right.

2.  **Functional Verification**:
    - **CRITICAL**: Select "Week" from the filter. The chart should display a line graph of the habit's score for each of the past several weeks.
    - Select "Month" from the filter. The chart must update to show the score for each of the past several months.
    - Test the "Quarter" and "Year" filters and verify the chart updates correctly for each selection.

3.  **Data Accuracy Verification**:
    - Create a test habit with a known history. For example, a habit that was completed 50% of the time last month and 75% of the time this month.
    - When viewing the chart with the "Month" filter, verify that the data points on the line graph accurately reflect `50%` and `75%` for the respective months.

4.  **Theme Verification**:
    - Switch the device between **Light and Dark modes**. Verify that all chart elements (text, grid lines, graph line, and points) adapt correctly to the theme.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**. [cite: 0_promptGuidelines.md]
- It is the **single source of truth** for styling decisions. [cite: 0_promptGuidelines.md]

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory). [cite: 0_promptGuidelines.md]
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. [cite: 0_promptGuidelines.md]
- The reference project serves as a **blueprint** for implementation. [cite: 0_promptGuidelines.md]
- This step is mandatory. **Do not proceed to implementation without this step.** [cite: 0_promptGuidelines.md]

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured. [cite: 0_promptGuidelines.md]
- Even for new features, existing components or utility functions may be reusable. [cite: 0_promptGuidelines.md]
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code. [cite: 0_promptGuidelines.md]

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development. [cite: 0_promptGuidelines.md]
- Keep the codebase **organized and clutter-free**. [cite: 0_promptGuidelines.md]

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions. [cite: 0_promptGuidelines.md]
- Immediately pause and seek clarification either from the project lead or directly from me. [cite: 0_promptGuidelines.md]
- It is better to get clarity than to redo or fix avoidable mistakes later. [cite: 0_promptGuidelines.md]

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use. [cite: 0_promptGuidelines.md]
- This includes obsolete routes, modules, features, or legacy logic. [cite: 0_promptGuidelines.md]