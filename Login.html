<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>UHabits Sign In</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet"/>
<style>
        body {
            font-family: 'Nunito Sans', sans-serif;
        }
        :root {
            --background: #FFFFFF;
            --background-darker: #F7FAFC;
            --surface-variant: #FFFFFF;
            --text-primary: #2D3748;
            --accent-primary: #38B2AC;
            --accent-primary-logo: #64C5BF;--text-secondary: #718096;
            --error: #E53E3E;
            --icon-color: #A0AEC0;
        }
        @media (prefers-color-scheme: dark) {
            :root {
                --background: #121826;
                --background-darker: #1A202C;
                --surface-variant: #1F2937;
                --text-primary: #E2E8F0;
                --accent-primary: #81E6D9;
                --accent-primary-logo: #A8F0E5;--text-secondary: #A0AEC0;
                --icon-color: #9CA3AF;
            }
        }
        .bg-gradient-custom {
            background-image: linear-gradient(to bottom, var(--background), var(--background-darker));
        }
        .bg-surface-variant { background-color: var(--surface-variant); }
        .text-text-primary { color: var(--text-primary); }
        .bg-accent-primary { background-color: var(--accent-primary); }
        .text-accent-primary { color: var(--accent-primary); }
        .text-text-secondary { color: var(--text-secondary); }
        .text-error { color: var(--error); }
        .text-icon-color { color: var(--icon-color); }
        .shadow-input { box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1); }
        .shadow-button { box-shadow: 0px 4px 8px rgba(56, 178, 172, 0.3); }
        .material-symbols-outlined {
          font-variation-settings:
          'FILL' 0,
          'wght' 300,
          'GRAD' 0,
          'opsz' 24
        }
        .text-accent-primary-logo { color: var(--accent-primary-logo); }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gradient-custom">
<div class="flex flex-col items-center justify-center min-h-screen px-5">
<div class="w-full max-w-sm text-center">
<svg class="w-16 h-16 mx-auto mb-4 text-accent-primary-logo" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
<h1 class="text-text-primary font-bold text-2xl">UHabits</h1>
<p class="text-text-primary text-sm mt-1 mb-10">Build Better Habits, Together</p>
<div class="space-y-6 text-left">
<div class="relative">
<span class="material-symbols-outlined absolute left-4 top-1/2 -translate-y-1/2 text-icon-color">
            person
            </span>
<input class="w-full bg-surface-variant text-text-primary text-sm pl-12 pr-4 py-3 rounded-[24px] placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary shadow-input" placeholder="Email" type="email"/>
</div>
<div class="relative">
<span class="material-symbols-outlined absolute left-4 top-1/2 -translate-y-1/2 text-icon-color">
            lock
            </span>
<input class="w-full bg-surface-variant text-text-primary text-sm pl-12 pr-4 py-3 rounded-[24px] placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary shadow-input" placeholder="Password" type="password"/>
</div>
</div>
<p class="text-error text-xs mt-4 text-left">Wrong password. Please try again.</p>
<button class="w-full bg-accent-primary text-white text-sm font-medium py-3 mt-6 rounded-[24px] shadow-button">
                    Sign In
                </button>
<div class="text-center mt-6">
<p class="text-text-secondary text-sm">
                        Don't have an account?
                        <a class="font-bold text-accent-primary" href="#">Sign Up</a>
</p>
</div>
</div>
</div>

</body></html>