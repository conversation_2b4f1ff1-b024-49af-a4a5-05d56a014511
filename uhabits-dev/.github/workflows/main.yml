name: Build & Test
on:
  pull_request:
    paths-ignore:
    - '**.md'
  push:
    paths-ignore:
    - '**.md'
jobs:
  Test:
    runs-on: self-hosted
    timeout-minutes: 30
    steps:
    - name: Check out source code
      uses: actions/checkout@v4

    - name: Build project
      run: ./build.sh build

    - name: Run Android tests
      run: ./build.sh android-tests-parallel 28 29 30 32 33 34

    - name: Upload artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: build
        path: |
          build/*log
          uhabits-android/build/outputs

