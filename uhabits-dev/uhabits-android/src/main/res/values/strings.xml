<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<resources
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingTranslation">

    <string name="app_name" >Loop Habit Tracker</string>
    <string name="main_activity_title">Habits</string>
    <string name="action_settings">Settings</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="archive">Archive</string>
    <string name="unarchive">Unarchive</string>
    <string name="add_habit">Add habit</string>
    <string name="color_picker_default_title">Change color</string>
    <string name="toast_habit_created">Habit created</string>
    <plurals name="toast_habits_changed">
        <item quantity="one">Habit changed</item>
        <item quantity="other">Habits changed</item>
    </plurals>
    <plurals name="toast_habits_deleted">
        <item quantity="one">Habit deleted</item>
        <item quantity="other">Habits deleted</item>
    </plurals>
    <plurals name="toast_habits_archived">
        <item quantity="one">Habit archived</item>
        <item quantity="other">Habits archived</item>
    </plurals>
    <plurals name="toast_habits_unarchived">
        <item quantity="one">Habit unarchived</item>
        <item quantity="other">Habits unarchived</item>
    </plurals>
    <string name="title_activity_show_habit" translatable="false"/>
    <string name="overview">Overview</string>
    <string name="habit_strength">Habit strength</string>
    <string name="history">History</string>
    <string name="clear">Clear</string>
    <string name="reminder">Reminder</string>
    <string name="save">Save</string>
    <string name="streaks">Streaks</string>
    <string name="no_habits_found">You have no active habits</string>
    <string name="no_habits_left_to_do">You\'re all done for today!</string>
    <string name="long_press_to_toggle">Press-and-hold to check or uncheck</string>
    <string name="reminder_off">Off</string>
    <string name="create_habit">Create habit</string>
    <string name="edit_habit">Edit habit</string>
    <string name="check">Check</string>
    <string name="snooze">Later</string>
    <string name="intro_title_1">Welcome</string>
    <string name="intro_description_1">Loop Habit Tracker helps you create and maintain good habits.</string>
    <string name="intro_title_2">Create some new habits</string>
    <string name="intro_description_2">Every day, after performing your habit, put a checkmark on the app.</string>
    <string name="intro_title_4">Track your progress</string>
    <string name="intro_description_4">Detailed graphs show you how your habits improved over time.</string>
    <string name="interval_15_minutes">15 minutes</string>
    <string name="interval_30_minutes">30 minutes</string>
    <string name="interval_1_hour">1 hour</string>
    <string name="interval_2_hour">2 hours</string>
    <string name="interval_4_hour">4 hours</string>
    <string name="interval_8_hour">8 hours</string>
    <string name="interval_24_hour">24 hours</string>
    <string name="interval_always_ask">Always ask</string>
    <string name="interval_custom">Custom...</string>
    <string name="pref_toggle_title">Toggle with short press</string>
    <string name="pref_toggle_description_2">Put checkmarks with a single tap instead of press-and-hold.</string>
    <string name="pref_rate_this_app">Rate this app on Google Play</string>
    <string name="pref_send_feedback">Send feedback to developer</string>
    <string name="pref_view_source_code">View source code at GitHub</string>
    <string name="links">Links</string>
    <string name="name">Name</string>
    <string name="settings">Settings</string>
    <string name="select_snooze_delay">Select snooze delay</string>
    <string name="hint_title">Did you know?</string>
    <string name="hint_drag">To rearrange the entries, press-and-hold on the name of the habit, then drag it to the correct place.</string>
    <string name="hint_landscape">You can see more days by putting your phone in landscape mode.</string>
    <plurals name="delete_habits_title">
        <item quantity="one">Delete habit?</item>
        <item quantity="other">Delete habits?</item>
    </plurals>
    <plurals name="delete_habits_message">
        <item quantity="one">The habit will be permanently deleted. This action cannot be undone.</item>
        <item quantity="other">The habits will be permanently deleted. This action cannot be undone.</item>
    </plurals>
    <string name="habit_not_found">Habit deleted / not found</string>
    <string name="weekends">Weekends</string>
    <string name="any_weekday">Monday to Friday</string>
    <string name="any_day">Any day of the week</string>
    <string name="select_weekdays">Select days</string>
    <string name="export_to_csv">Export as CSV</string>
    <string name="done_label">Done</string>
    <string name="clear_label">Clear</string>
    <string name="select_hours">Select hours</string>
    <string name="select_minutes">Select minutes</string>
    <string-array name="hints">
        <item>@string/hint_drag</item>
        <item>@string/hint_landscape</item>
    </string-array>
    <string name="about">About</string>
    <string name="translators">Translators</string>
    <string name="developers">Developers</string>
    <string name="version_n">Version %s</string>
    <string name="frequency">Frequency</string>
    <string name="checkmark">Checkmark</string>
    <string name="checkmark_stack_widget" formatted="false">Checkmark Stack Widget</string>
    <string name="frequency_stack_widget" formatted="false">Frequency Stack Widget</string>
    <string name="score_stack_widget" formatted="false">Score Stack Widget</string>
    <string name="history_stack_widget" formatted="false">History Stack Widget</string>
    <string name="streaks_stack_widget" formatted="false">Streaks Stack Widget</string>
    <string name="best_streaks">Best streaks</string>
    <string name="every_day">Every day</string>
    <string name="every_week">Every week</string>
    <string name="help">Help &amp; FAQ</string>
    <string name="could_not_export">Failed to export data.</string>
    <string name="could_not_import">Failed to import data.</string>
    <string name="file_not_recognized">File not recognized.</string>
    <string name="habits_imported">Habits imported successfully.</string>
    <string name="import_data">Import data</string>
    <string name="export_full_backup">Export full backup</string>
    <string name="import_data_summary">Supports full backups exported by this app, as well as files generated by Tickmate, HabitBull or Rewire. See FAQ for more information.</string>
    <string name="export_as_csv_summary">Generates files that can be opened by spreadsheet software such as Microsoft Excel or OpenOffice Calc. This file cannot be imported back.</string>
    <string name="export_full_backup_summary">Generates a file that contains all your data. This file can be imported back.</string>
    <string name="bug_report_failed">Failed to generate bug report.</string>
    <string name="generate_bug_report">Generate bug report</string>
    <string name="troubleshooting">Troubleshooting</string>
    <string name="help_translate">Help translate this app</string>
    <string name="night_mode">Dark theme</string>
    <string name="use_pure_black">Use pure black in dark theme</string>
    <string name="pure_black_description">Replaces gray backgrounds with pure black in dark theme. Reduces battery usage in phones with AMOLED display.</string>
    <string name="interface_preferences">Interface</string>
    <string name="reverse_days">Reverse order of days</string>
    <string name="reverse_days_description">Show days in reverse order on the main screen.</string>
    <string name="day">Day</string>
    <string name="week">Week</string>
    <string name="month">Month</string>
    <string name="quarter">Quarter</string>
    <string name="year">Year</string>
    <string name="total">Total</string>
    <string name="yes_or_no">Yes or No</string>
    <string name="every_x_days">Every %d days</string>
    <string name="every_x_weeks">Every %d weeks</string>
    <string name="score">Score</string>
    <string name="reminder_sound">Reminder sound</string>
    <string name="none">None</string>
    <string name="filter">Filter</string>
    <string name="hide_completed">Hide completed</string>
    <string name="hide_entered">Hide entered</string>
    <string name="hide_archived">Hide archived</string>
    <string name="sticky_notifications">Make notifications sticky</string>
    <string name="sticky_notifications_description">Prevents notifications from being swiped away.</string>
    <string name="led_notifications">Notification light</string>
    <string name="led_notifications_description">Shows a blinking light for reminders. Only available in phones with LED notification lights.</string>
    <string name="repair_database">Repair database</string>
    <string name="database_repaired">Database repaired.</string>
    <string name="uncheck">Uncheck</string>
    <string name="toggle">Toggle</string>
    <string name="action">Action</string>
    <string name="habit">Habit</string>
    <string name="sort">Sort</string>
    <string name="manually">Manually</string>
    <string name="by_name">By name</string>
    <string name="by_color">By color</string>
    <string name="by_score">By score</string>
    <string name="by_status">By status</string>
    <string name="export">Export</string>
    <string name="long_press_to_edit">Press-and-hold to change the value</string>
    <string name="value">Value</string>
    <string name="calendar">Calendar</string>
    <string name="unit">Unit</string>
    <string name="target_type">Target Type</string>
    <string name="target_type_at_least">At least</string>
    <string name="target_type_at_most">At most</string>
    <string name="example_question_boolean">e.g. Did you exercise today?</string>
    <string name="question">Question</string>
    <string name="target">Target</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="customize_notification_summary">Change sound, vibration, light and other notification settings</string>
    <string name="customize_notification">Customize notifications</string>
    <string name="pref_view_privacy">View privacy policy</string>
    <string name="view_all_contributors">View all contributors…</string>
    <string name="database">Database</string>
    <string name="widget_opacity_title">Widget opacity</string>
    <string name="widget_opacity_description">Makes widgets more transparent or more opaque in your home screen.</string>
    <string name="first_day_of_the_week">First day of the week</string>
    <string name="default_reminder_question">Have you completed this habit today?</string>
    <string name="notes">Notes</string>
    <string name="example_notes">(Optional)</string>
    <string name="yes_or_no_example">e.g. Did you wake up early today? Did you exercise? Did you play chess?</string>
    <string name="measurable">Measurable</string>
    <string name="measurable_example">e.g. How many miles did you run today? How many pages did you read?</string>
    <string name="x_times_per_week">%d times per week</string>
    <string name="x_times_per_month">%d times per month</string>
    <string name="x_times_per_y_days">%d times in %d days</string>
    <string name="yes_or_no_short_example">e.g. Exercise</string>
    <string name="color">Color</string>
    <string name="example_target">e.g. 15</string>
    <string name="measurable_short_example">e.g. Run</string>
    <string name="measurable_question_example">e.g. How many miles did you run today?</string>
    <string name="measurable_units_example">e.g. miles</string>
    <string name="every_month">Every month</string>
    <string name="validation_cannot_be_blank">Cannot be blank</string>
    <string name="today">Today</string>
    <string name="enter">Enter</string>
    <string name="no_habits">No habits found</string>
    <string name="no_numerical_habits">No measurable habits found</string>
    <string name="no_boolean_habits">No yes-or-no habits found</string>
    <string name="increment">Increment</string>
    <string name="decrement">Decrement</string>
    <string name="pref_skip_title">Enable skip days</string>
    <string name="skip_day">Skip</string>
    <string name="pref_skip_description">Toggle twice to add a skip instead of a checkmark. Skips keep your score unchanged and don\'t break your streak.</string>
    <string name="pref_unknown_title">Show question marks for missing data</string>
    <string name="pref_unknown_description">Differentiate days without data from actual lapses. To enter a lapse, toggle twice.</string>
    <string name="you_are_now_a_developer">You are now a developer</string>
    <string name="activity_not_found">No app was found to support this action</string>
    <string name="pref_midnight_delay_title">Extend day a few hours past midnight</string>
    <string name="pref_midnight_delay_description">Wait until 3:00 AM to show a new day. Useful if you typically go to sleep after midnight. Requires app restart.</string>
    <string name="pref_animations_title">Disable animations</string>
    <string name="pref_animations_description">Disable confetti animation after adding a checkmark.</string>
</resources>
