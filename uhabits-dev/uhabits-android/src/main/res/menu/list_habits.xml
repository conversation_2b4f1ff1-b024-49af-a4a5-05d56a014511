<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MainActivity">

    <item
        android:id="@+id/actionCreateHabit"
        android:enabled="true"
        android:icon="?iconAdd"
        android:title="@string/add_habit"
        app:showAsAction="always"/>


    <item
        android:id="@+id/action_filter"
        android:icon="?iconFilter"
        android:title="@string/filter"
        app:showAsAction="always">
        <menu>
            <item
                android:id="@+id/actionHideArchived"
                android:checkable="true"
                android:enabled="true"
                android:title="@string/hide_archived"/>

            <item
                android:id="@+id/actionHideCompleted"
                android:checkable="true"
                android:enabled="true"
                android:title="@string/hide_completed"/>

            <item android:title="@string/sort">
                <menu>
                    <item
                        android:id="@+id/actionSortManual"
                        android:title="@string/manually"/>

                    <item
                        android:id="@+id/actionSortName"
                        android:title="@string/by_name"/>

                    <item
                        android:id="@+id/actionSortColor"
                        android:title="@string/by_color"/>

                    <item
                        android:id="@+id/actionSortScore"
                        android:title="@string/by_score"/>

                    <item
                        android:id="@+id/actionSortStatus"
                        android:title="@string/by_status"/>
                </menu>
            </item>
        </menu>
    </item>

    <item
        android:id="@+id/actionToggleNightMode"
        android:checkable="true"
        android:enabled="true"
        android:orderInCategory="50"
        android:title="@string/night_mode"
        app:showAsAction="never"/>

    <item
        android:id="@+id/actionSettings"
        android:orderInCategory="100"
        android:title="@string/action_settings"
        app:showAsAction="never"/>

    <item
        android:id="@+id/actionFAQ"
        android:orderInCategory="100"
        android:title="@string/help"
        app:showAsAction="never"/>

    <item
        android:id="@+id/actionAbout"
        android:orderInCategory="100"
        android:title="@string/about"
        app:showAsAction="never"/>

</menu>
