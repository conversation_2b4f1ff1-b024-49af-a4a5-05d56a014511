<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="?android:attr/buttonBarStyle"
    android:layout_width="@dimen/date_picker_component_width"
    android:layout_height="wrap_content"
    android:orientation="horizontal" >
    
    <Button
        android:id="@+id/clear"
        style="?android:attr/buttonBarButtonStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:minHeight="48dp"
        android:text="@string/clear"
        android:textSize="@dimen/done_label_size"
        android:textColor="@color/done_text_color" />

    <Button
        android:id="@+id/done"
        style="?android:attr/buttonBarButtonStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:minHeight="48dp"
        android:text="@string/done_label"
        android:textSize="@dimen/done_label_size"
        android:textColor="@color/done_text_color" />
</LinearLayout>
