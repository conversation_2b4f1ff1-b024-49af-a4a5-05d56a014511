<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#a0000000"
    android:clickable="true"
    android:id="@+id/background"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonYesNo"
        style="@style/SelectHabitTypeButton">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/SelectHabitTypeButtonTitle"
            android:text="@string/yes_or_no" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/SelectHabitTypeButtonBody"
            android:text="@string/yes_or_no_example" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonMeasurable"
        style="@style/SelectHabitTypeButton">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/SelectHabitTypeButtonTitle"
            android:text="@string/measurable" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/SelectHabitTypeButtonBody"
            android:text="@string/measurable_example" />
    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:id="@+id/buttonSubjective"-->
<!--        style="@style/SelectHabitTypeButton">-->

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            style="@style/SelectHabitTypeButtonTitle"-->
<!--            android:text="@string/subjective" />-->

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            style="@style/SelectHabitTypeButtonBody"-->
<!--            android:text="@string/subjective_example" />-->
<!--    </LinearLayout>-->

</LinearLayout>