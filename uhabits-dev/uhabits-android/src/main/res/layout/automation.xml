<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical"
              android:background="?attr/windowBackgroundColor">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorPrimary"
            android:elevation="2dp"
            android:gravity="end"
            android:minHeight="?attr/actionBarSize"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:title="@string/app_name"
            app:titleTextColor="@color/white">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSave"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:text="@string/save"
                android:textColor="@color/white"
                app:rippleColor="@color/white"
                app:strokeColor="@color/white" />
        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:id="@+id/formPanel"
        style="@style/dialogFormPanel"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="?attr/contrast0">

        <FrameLayout style="@style/FormOuterBox">
            <LinearLayout style="@style/FormInnerBox">
                <TextView
                    style="@style/FormLabel"
                    android:text="@string/habit" />

                <androidx.appcompat.widget.AppCompatSpinner
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:id="@+id/habitSpinner"
                    android:text="@string/check" />
            </LinearLayout>
        </FrameLayout>

        <FrameLayout style="@style/FormOuterBox">
            <LinearLayout style="@style/FormInnerBox">
                <TextView
                    style="@style/FormLabel"
                    android:text="@string/action" />

                <androidx.appcompat.widget.AppCompatSpinner
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:id="@+id/actionSpinner"
                    android:entries="@array/actions_yes_no"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:text="@string/check" />
            </LinearLayout>
        </FrameLayout>

    </LinearLayout>

</LinearLayout>