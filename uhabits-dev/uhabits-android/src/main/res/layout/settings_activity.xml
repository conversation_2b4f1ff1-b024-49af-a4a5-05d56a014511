<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:tools="http://schemas.android.com/tools"
             xmlns:app="http://schemas.android.com/apk/res-auto"
             android:id="@+id/container"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             tools:context=".activities.settings.SettingsActivity"
             tools:ignore="MergeRootFrame">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        app:popupTheme="?toolbarPopupTheme"
        style="@style/Toolbar"/>

    <fragment
        android:id="@+id/fragment"
        android:name="org.isoron.uhabits.activities.settings.SettingsFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_below="@id/toolbar"/>

</RelativeLayout>
