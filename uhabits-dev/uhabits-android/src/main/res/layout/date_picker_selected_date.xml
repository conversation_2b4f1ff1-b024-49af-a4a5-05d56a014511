<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/day_picker_selected_date_layout"
    android:layout_width="@dimen/date_picker_component_width"
    android:layout_height="0dip"
    android:layout_weight="1"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical" >

    <com.android.datetimepicker.AccessibleLinearLayout
        android:id="@+id/date_picker_month_and_day"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:clickable="true"
        android:orientation="vertical"
        android:textColor="@color/date_picker_selector" >

        <TextView
            android:id="@+id/date_picker_month"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:gravity="center_horizontal|bottom"
            android:includeFontPadding="false"
            android:textColor="@color/date_picker_selector"
            android:textSize="@dimen/selected_date_month_size" />

        <TextView
            android:id="@+id/date_picker_day"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="-10dip"
            android:layout_marginTop="-10dip"
            android:duplicateParentState="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/date_picker_selector"
            android:textSize="@dimen/selected_date_day_size" />
    </com.android.datetimepicker.AccessibleLinearLayout>

    <com.android.datetimepicker.AccessibleTextView
        android:id="@+id/date_picker_year"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center_horizontal|top"
        android:includeFontPadding="false"
        android:textColor="@color/date_picker_selector"
        android:textSize="@dimen/selected_date_year_size" />

</LinearLayout>