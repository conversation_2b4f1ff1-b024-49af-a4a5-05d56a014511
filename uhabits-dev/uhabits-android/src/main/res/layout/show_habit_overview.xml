<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<merge xmlns:android="http://schemas.android.com/apk/res/android"
       xmlns:habit="http://isoron.org/android">

    <TextView
        android:id="@+id/title"
        style="@style/CardHeader"
        android:text="@string/overview"/>

    <LinearLayout
        android:id="@+id/llOverview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5">

            <org.isoron.uhabits.activities.common.views.RingView
                android:id="@+id/scoreRing"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                habit:percentage="0"
                habit:textSize="12"
                habit:thickness="5"/>

        </FrameLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            android:orientation="vertical">

            <TextView
                android:id="@+id/scoreLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/score"
                android:textColor="?attr/contrast60"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            android:orientation="vertical">

            <TextView
                android:id="@+id/monthDiffLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/month"
                android:textColor="?attr/contrast60"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            android:orientation="vertical">

            <TextView
                android:id="@+id/yearDiffLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/year"
                android:textColor="?attr/contrast60"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            android:orientation="vertical">

            <TextView
                android:id="@+id/totalCountLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/total"
                android:textColor="?attr/contrast60"/>

        </LinearLayout>
    </LinearLayout>
</merge>