<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    android:paddingStart="10dp"
    android:paddingEnd="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="5dp"
        android:baselineAligned="false">

        <FrameLayout
            style="@style/FormOuterBox"
            android:layout_width="0dp"
            android:layout_weight="1">
            <LinearLayout style="@style/DialogFormInnerBox">

                <TextView
                    style="@style/DialogFormLabel"
                    android:text="@string/value" />

                <LinearLayout
                    android:orientation="horizontal"
                    android:gravity="center_horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <NumberPicker
                        android:id="@+id/picker"
                        android:layout_gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <TextView
                        android:id="@+id/tvSeparator"
                        android:layout_gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <NumberPicker
                        android:id="@+id/picker2"
                        android:layout_gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:id="@+id/tvUnit"
                        android:layout_gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                </LinearLayout>

            </LinearLayout>
        </FrameLayout>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="5dp"
        android:baselineAligned="false">

        <FrameLayout
            style="@style/FormOuterBox"
            android:layout_width="0dp"
            android:layout_weight="1">

            <LinearLayout style="@style/DialogFormInnerBox">

                <TextView
                    style="@style/DialogFormLabel"
                    android:text="@string/notes" />

                <EditText
                    android:id="@+id/etNotes"
                    android:inputType="textCapSentences|textMultiLine"
                    style="@style/FormInput"
                    android:scrollbars="vertical"
                    android:hint="@string/example_notes"/>

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</LinearLayout>