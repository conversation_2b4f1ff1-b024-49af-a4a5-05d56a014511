<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2013 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/time_picker_dialog"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:focusable="true"
    android:orientation="vertical" >

    <FrameLayout
        android:id="@+id/time_display_background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white" >

        <include
            android:layout_width="match_parent"
            android:layout_height="@dimen/header_height"
            android:layout_gravity="center"
            layout="@layout/time_header_label" />
    </FrameLayout>

    <com.android.datetimepicker.time.RadialPickerLayout
        android:id="@+id/time_picker"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/picker_dimen"
        android:layout_gravity="center"
        android:background="@color/circle_background"
        android:focusable="true"
        android:focusableInTouchMode="true" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dip"
        android:background="@color/line_background" />

    <androidx.appcompat.widget.LinearLayoutCompat
        style="?android:attr/buttonBarStyle"
        android:layout_width="@dimen/date_picker_component_width"
        android:layout_height="wrap_content"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.AppCompatButton
            style="?android:attr/buttonBarButtonStyle"
            android:id="@+id/clear_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp"
            android:textColor="#333"
            android:text="@string/clear_label"
            android:textSize="@dimen/done_label_size" />

        <androidx.appcompat.widget.AppCompatButton
            style="?android:attr/buttonBarButtonStyle"
            android:id="@+id/done_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:minHeight="48dp"
            android:textColor="#333"
            android:text="@string/done_label"
            android:textSize="@dimen/done_label_size" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</LinearLayout>