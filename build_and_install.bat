@echo off

REM Check if Android device is connected
adb devices | find "device"
if %ERRORLEVEL% NEQ 0 (
    echo No connected devices found
    pause
    exit /b 1
)

REM Clean and build project
call gradlew clean assembleDebug
if %ERRORLEVEL% NEQ 0 (
    echo Build failed
    pause
    exit /b 1
)

REM Install APK
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% NEQ 0 (
    echo Installation failed
    pause
    exit /b 1
)

echo Build and installation successful!
pause