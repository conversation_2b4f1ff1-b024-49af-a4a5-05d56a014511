# Project Handover Report: UHabits_99

**To my successor:** Welcome to the UHabits_99 project. It is now your responsibility to continue as the lead prompt engineer for this application. This document contains a comprehensive overview of the project's vision, current status, design philosophy, and the specific, effective workflow we have established. Please review it carefully.

### 1. What is Our Project All About?

We are building **UHabits_99**, a native Android (Kotlin) habit tracker application.

**The Vision:** To create a powerful, flexible, and user-friendly tool that helps users build and maintain positive habits. The app is designed to be both simple for new users and powerful for those who want detailed tracking and customization.

**Core Features:**

- **Dual Habit Types:** The app supports both simple "Yes/No" habits (e.g., "Meditate") and "Measurable" habits (e.g., "Run 5 km").
- **Detailed Tracking:** Users can track their habits on a daily basis, view streaks, and see completion percentages.
- **Customization:** The app is being built with a strong emphasis on user control, allowing for settings like a custom start day of the week and highly flexible habit scheduling.
- **Clean, Modern UI:** We follow a strict style guide to ensure the user interface is intuitive, clean, and visually appealing.

### 2. What Did We Achieve Till Now? (Current Status)

We are in the middle of **Stage 1: Finalize Core Settings & Tracking** as outlined in the `Project Roadmap Habit Tracker v1.md` document.

**Completed Milestones:**

- **Core Habit Tracking:** The fundamental logic for creating, displaying, and tracking both "Yes/No" and "Measurable" habits is complete and working.
- **Home Screen UI Refinement:** We have successfully built and debugged the main screen, including:
    - A horizontally scrolling, sticky date header.
    - A robust and reliable system for marking habits as complete.
    - A clear and intuitive UI for displaying measurable habit targets and daily logged values.
- **Key Bug Fixes:** We have resolved several critical bugs related to date calculations, UI state management, and database performance.

**Our Immediate Next Task:**
We have just finished the planning and design phase for the **Customizable Habit Frequency** feature. We have a detailed, 4-phase roadmap to implement it. Your first task is to begin this roadmap by generating the prompt for **Phase 1: Database and Data Model Foundation**.

### 3. How to Create Effective Prompts (Our Workflow)

Our collaboration is built on a specific and highly effective prompt engineering process. Your primary role is to translate the user's goals into detailed requirement documents (prompts) for the AI developer.

**The Core Principle: Focus on the "WHAT," not the "HOW."**
Your prompts must describe the desired outcome, behavior, and logic in exhaustive detail. Do not provide implementation suggestions. The AI developer is responsible for the "how."

**The Anatomy of a Perfect Prompt:**

1. **Objective:** Start with a clear, one-sentence goal for the task.
2. **Visual References:** If the user provides images, refer to them directly by filename (e.g., `24.jpg`). Visuals are our most effective communication tool.
3. **Detailed Implementation Plan:** This is the heart of the prompt.
    - Break the feature down into logical, numbered steps.
    - Use bullet points to describe every detail of the required behavior, UI changes, and logic. Be explicit and leave nothing to assumption.
4. **Verification / Testing Section:** Clearly define the expected outcome and list the specific test cases the developer must use to confirm that the feature is working correctly.
5. **Mandatory Development Guidelines:** **Every prompt must end with the full, unaltered block of mandatory guidelines.** This is a non-negotiable rule.

**Our Collaborative Process:**

1. The user provides a high-level goal.
2. You will review the goal, ask clarifying questions, and propose a design or implementation plan.
3. The user will discuss and approve the plan.
4. **Only after approval**, you will generate the final, detailed prompt in `.md` format.

### 4. Our Design Philosophy

- **Clarity Over Clutter:** The UI must be intuitive. Information should be presented cleanly and clearly. If a feature adds complexity, we must find a design that simplifies it for the user (e.g., our multi-screen approach for habit frequency).
- **User Control is Key:** We prioritize features that give the user power and flexibility (e.g., custom start day of the week, detailed recurrence rules).
- **Iterate and Refine:** We build features in logical steps, test them, and then polish them based on feedback. We are not afraid to refactor or redesign a feature to get it right.

### 5. Essential Project Resources

To succeed, you must constantly refer to these three key files:

1. **`0_promptGuidelines.md`:** This is the master document governing our entire interaction. Adhere to it strictly.
2. **`style.md`:** This is the single source of truth for all UI and styling decisions.
3. **`uhabits-dev/` folder:** This is the reference project. It is **mandatory** to study how it implements similar features before generating any prompt for a new feature.

### A Note to My Successor

It has been a privilege to work on this project. The user is an excellent collaborator with a clear vision. Your role is to continue the process we have built: listen, clarify, design, and then translate that design into a flawless set of instructions.

Please take a moment to review this document and the key project files.

**Do you have any questions or need any clarifications before we begin with Phase 1 of the Customizable Habit Frequency feature?**