package com.example.uhabits_99.ui.theme

import androidx.compose.ui.graphics.Color

// Light Theme Colors (from style.md)
val LightBackground = Color(0xFFFFFFFF)           // #FFFFFF
val LightTextPrimary = Color(0xFF2D3748)          // #2D3748 - 45, 55, 72
val LightTextSecondary = Color(0xFF718096)        // #718096 - 113, 128, 150
val LightAccentPrimary = Color(0xFF38B2AC)        // #38B2AC - 56, 178, 172
val LightDivider = Color(0xFFE2E8F0)              // #E2E8F0 - 226, 232, 240
val LightSurfaceVariant = Color(0xFFF7FAFC)       // #F7FAFC - 247, 250, 252

// Dark Theme Colors (from style.md)
val DarkBackground = Color(0xFF121826)            // #121826 - 18, 24, 38
val DarkTextPrimary = Color(0xFFE2E8F0)           // #E2E8F0 - 226, 232, 240
val DarkTextSecondary = Color(0xFFA0AEC0)         // #A0AEC0 - 160, 174, 192
val DarkAccentPrimary = Color(0xFF81E6D9)         // #81E6D9 - 129, 230, 217
val DarkDivider = Color(0xFF2D3748)               // #2D3748 - 45, 55, 72
val DarkSurfaceVariant = Color(0xFF1A202C)        // #1A202C - 26, 32, 44

// Legacy colors (keeping for compatibility)
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)