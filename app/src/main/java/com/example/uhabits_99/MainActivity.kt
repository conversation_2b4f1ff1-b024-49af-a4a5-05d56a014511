package com.example.uhabits_99

import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.uhabits_99.ui.theme.UHabits_99Theme
import com.example.habits9.ui.home.HomeScreen
import com.example.habits9.ui.details.HabitDetailsScreen
import com.example.habits9.ui.habittypeselection.HabitTypeSelectionScreen
import com.example.habits9.ui.createyesnohabit.CreateYesNoHabitScreen
import com.example.habits9.ui.createmeasurablehabit.CreateMeasurableHabitScreen
import com.example.habits9.ui.managesections.ManageSectionsScreen
import com.example.habits9.ui.settings.SettingsScreen
import com.example.habits9.ui.auth.AuthScreen
import com.example.habits9.ui.auth.VerificationScreen
import com.example.habits9.ui.habitreorder.HabitReorderScreen
import com.google.firebase.auth.FirebaseAuth
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            UHabits_99Theme {
                val navController = rememberNavController()

                // Check authentication state to determine start destination
                val currentUser = FirebaseAuth.getInstance().currentUser
                val startDestination = if (currentUser?.isEmailVerified == true) "home" else "auth"

                NavHost(
                    navController = navController,
                    startDestination = startDestination
                ) {
                    composable("auth") {
                        AuthScreen(
                            onNavigateToHome = {
                                navController.navigate("home") {
                                    // Clear the auth screen from back stack
                                    popUpTo("auth") { inclusive = true }
                                }
                            },
                            onNavigateToVerification = { email ->
                                navController.navigate("verification/$email")
                            }
                        )
                    }
                    composable("verification/{email}") { backStackEntry ->
                        val email = backStackEntry.arguments?.getString("email") ?: ""
                        VerificationScreen(
                            email = email,
                            onBackToLogin = {
                                navController.navigate("auth") {
                                    popUpTo("verification/{email}") { inclusive = true }
                                }
                            }
                        )
                    }
                    composable("home") {
                        HomeScreen(
                            onNavigateToHabitDetails = { habitId ->
                                navController.navigate("habit_details/$habitId")
                            },
                            onNavigateToCreateHabit = {
                                navController.navigate("habit_type_selection")
                            },
                            onNavigateToSettings = {
                                navController.navigate("settings")
                            },
                            onNavigateToManageSections = {
                                navController.navigate("manage_sections")
                            },
                            onNavigateToHabitReorder = {
                                navController.navigate("habit_reorder")
                            }
                        )
                    }
                    composable("habit_details/{habitId}") { backStackEntry ->
                        val habitIdString = backStackEntry.arguments?.getString("habitId")
                        val habitId = habitIdString?.toLongOrNull() ?: 0L
                        android.util.Log.d("MainActivity", "Navigating to habit details with habitId: $habitId (from string: $habitIdString)")
                        HabitDetailsScreen(
                            habitId = habitId,
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onEditClick = { editHabitId, habitType ->
                                when (habitType) {
                                    com.example.habits9.data.HabitType.YES_NO -> {
                                        navController.navigate("edit_yes_no_habit/$editHabitId")
                                    }
                                    com.example.habits9.data.HabitType.NUMERICAL -> {
                                        navController.navigate("edit_measurable_habit/$editHabitId")
                                    }
                                }
                            },
                            onDeleteClick = {
                                // After deletion, navigate back to home
                                navController.popBackStack("home", inclusive = false)
                            }
                        )
                    }
                    composable("habit_type_selection") {
                        HabitTypeSelectionScreen(
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onYesNoSelected = {
                                navController.navigate("create_yes_no_habit")
                            },
                            onMeasurableSelected = {
                                navController.navigate("create_measurable_habit")
                            }
                        )
                    }
                    composable("create_yes_no_habit") {
                        CreateYesNoHabitScreen(
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onSaveClick = {
                                // Navigate back to home after saving
                                navController.popBackStack("home", inclusive = false)
                            }
                        )
                    }
                    composable("create_measurable_habit") {
                        CreateMeasurableHabitScreen(
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onSaveClick = {
                                // Navigate back to home after saving
                                navController.popBackStack("home", inclusive = false)
                            }
                        )
                    }
                    composable("edit_yes_no_habit/{habitId}") { backStackEntry ->
                        val habitIdString = backStackEntry.arguments?.getString("habitId")
                        val habitId = habitIdString?.toLongOrNull() ?: 0L
                        CreateYesNoHabitScreen(
                            habitId = habitId, // Pass habitId for edit mode
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onSaveClick = {
                                // Navigate back to habit details after updating
                                navController.popBackStack()
                            }
                        )
                    }
                    composable("edit_measurable_habit/{habitId}") { backStackEntry ->
                        val habitIdString = backStackEntry.arguments?.getString("habitId")
                        val habitId = habitIdString?.toLongOrNull() ?: 0L
                        CreateMeasurableHabitScreen(
                            habitId = habitId, // Pass habitId for edit mode
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onSaveClick = {
                                // Navigate back to habit details after updating
                                navController.popBackStack()
                            }
                        )
                    }
                    composable("manage_sections") {
                        ManageSectionsScreen(
                            onBackClick = {
                                navController.popBackStack()
                            }
                        )
                    }
                    composable("settings") {
                        SettingsScreen(
                            onBackClick = {
                                navController.popBackStack()
                            },
                            onSignOut = {
                                navController.navigate("auth") {
                                    // Clear all screens from back stack
                                    popUpTo(0) { inclusive = true }
                                }
                            }
                        )
                    }
                    composable("habit_reorder") {
                        HabitReorderScreen(
                            onBackClick = {
                                navController.popBackStack()
                            }
                        )
                    }
                }
            }
        }
    }
}