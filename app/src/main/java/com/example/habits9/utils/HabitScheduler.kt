package com.example.habits9.utils

import com.example.habits9.data.DayOfWeek
import com.example.habits9.data.EnhancedFrequency
import com.example.habits9.data.FrequencyType
import com.example.habits9.data.Habit
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters

/**
 * Utility class for determining if a habit is scheduled for a specific date.
 * This is the core scheduling function that evaluates habit frequency rules.
 */
object HabitScheduler {

    /**
     * Determines if a habit is scheduled to occur on a specific date.
     *
     * @param habit The habit to check
     * @param date The date to check against
     * @return true if the habit is scheduled for the given date, false otherwise
     */
    fun isHabitScheduled(habit: Habit, date: LocalDate): <PERSON><PERSON><PERSON> {
        // Create an EnhancedFrequency object from the habit's frequency data
        val frequency = EnhancedFrequency.fromDatabaseValues(
            frequencyType = habit.frequencyType,
            repeatsEvery = habit.repeatsEvery,
            daysOfWeek = habit.daysOfWeek,
            dayOfMonth = habit.dayOfMonth,
            weekOfMonth = habit.weekOfMonth,
            dayOfWeekInMonth = habit.dayOfWeekInMonth
        )

        return isScheduledWithFrequency(frequency, date, habit.creationDate)
    }

    /**
     * Determines if a habit with the given frequency is scheduled for a specific date.
     * This function is separated from isHabitScheduled to make it easier to test.
     *
     * @param frequency The frequency configuration
     * @param date The date to check
     * @param creationTimestamp The timestamp when the habit was created (used as reference point)
     * @return true if the habit is scheduled for the given date, false otherwise
     */
    fun isScheduledWithFrequency(
        frequency: EnhancedFrequency,
        date: LocalDate,
        creationTimestamp: Long
    ): Boolean {
        // Use habit creation date as the reference point
        val creationDate = LocalDate.ofEpochDay(creationTimestamp / (24 * 60 * 60 * 1000))

        return when (frequency.type) {
            FrequencyType.DAILY -> isDailyHabitScheduled(frequency, date, creationDate)
            FrequencyType.WEEKLY -> isWeeklyHabitScheduled(frequency, date, creationDate)
            FrequencyType.MONTHLY -> isMonthlyHabitScheduled(frequency, date, creationDate)
        }
    }

    /**
     * Determines if a daily habit is scheduled for the given date.
     */
    private fun isDailyHabitScheduled(
        frequency: EnhancedFrequency,
        date: LocalDate,
        creationDate: LocalDate
    ): Boolean {
        // For daily habits, we need to check if the number of days since creation
        // is divisible by the repeatsEvery value
        val daysSinceCreation = ChronoUnit.DAYS.between(creationDate, date)
        
        // If repeatsEvery is 1, the habit occurs every day
        if (frequency.repeatsEvery == 1) return true
        
        // Otherwise, check if the days since creation is divisible by repeatsEvery
        return daysSinceCreation % frequency.repeatsEvery == 0L
    }

    /**
     * Determines if a weekly habit is scheduled for the given date.
     */
    private fun isWeeklyHabitScheduled(
        frequency: EnhancedFrequency,
        date: LocalDate,
        creationDate: LocalDate
    ): Boolean {
        // First, check if the day of week is selected
        val dayOfWeek = when (date.dayOfWeek) {
            java.time.DayOfWeek.MONDAY -> DayOfWeek.MONDAY
            java.time.DayOfWeek.TUESDAY -> DayOfWeek.TUESDAY
            java.time.DayOfWeek.WEDNESDAY -> DayOfWeek.WEDNESDAY
            java.time.DayOfWeek.THURSDAY -> DayOfWeek.THURSDAY
            java.time.DayOfWeek.FRIDAY -> DayOfWeek.FRIDAY
            java.time.DayOfWeek.SATURDAY -> DayOfWeek.SATURDAY
            java.time.DayOfWeek.SUNDAY -> DayOfWeek.SUNDAY
            else -> throw IllegalArgumentException("Invalid day of week")
        }

        // If no days are selected, default to all days
        val selectedDays = if (frequency.daysOfWeek.isEmpty()) {
            DayOfWeek.values().toList()
        } else {
            frequency.daysOfWeek
        }

        // If the day of week is not selected, the habit is not scheduled
        if (!selectedDays.contains(dayOfWeek)) {
            return false
        }

        // If repeatsEvery is 1, the habit occurs every week on the selected days
        if (frequency.repeatsEvery == 1) {
            return true
        }

        // Calculate the week number since creation
        val weeksSinceCreation = ChronoUnit.WEEKS.between(creationDate, date)

        // Check if the week number is divisible by repeatsEvery
        return weeksSinceCreation % frequency.repeatsEvery == 0L
    }

    /**
     * Determines if a monthly habit is scheduled for the given date.
     */
    private fun isMonthlyHabitScheduled(
        frequency: EnhancedFrequency,
        date: LocalDate,
        creationDate: LocalDate
    ): Boolean {
        // Calculate months since creation
        val monthsSinceCreation = ChronoUnit.MONTHS.between(creationDate, date)
        
        // Check if this month is in the schedule based on repeatsEvery
        if (monthsSinceCreation % frequency.repeatsEvery != 0L) {
            return false
        }

        // Now check if this specific day matches the monthly pattern
        return if (frequency.dayOfMonth != null) {
            // Simple case: specific day of month (e.g., the 15th)
            date.dayOfMonth == frequency.dayOfMonth
        } else if (frequency.weekOfMonth != null && frequency.dayOfWeekInMonth != null) {
            // Complex case: specific weekday in a specific week (e.g., 3rd Tuesday)
            isSpecificWeekdayInMonth(date, frequency.weekOfMonth, frequency.dayOfWeekInMonth)
        } else {
            // Default to the same day of month as creation date
            date.dayOfMonth == creationDate.dayOfMonth
        }
    }

    /**
     * Checks if the given date is the nth occurrence of a specific weekday in its month.
     * For example, is this date the 3rd Tuesday of the month?
     */
    private fun isSpecificWeekdayInMonth(
        date: LocalDate,
        weekOfMonth: Int,
        dayOfWeekInMonth: DayOfWeek
    ): Boolean {
        // Convert our DayOfWeek enum to java.time.DayOfWeek
        val javaTimeDay = when (dayOfWeekInMonth) {
            DayOfWeek.MONDAY -> java.time.DayOfWeek.MONDAY
            DayOfWeek.TUESDAY -> java.time.DayOfWeek.TUESDAY
            DayOfWeek.WEDNESDAY -> java.time.DayOfWeek.WEDNESDAY
            DayOfWeek.THURSDAY -> java.time.DayOfWeek.THURSDAY
            DayOfWeek.FRIDAY -> java.time.DayOfWeek.FRIDAY
            DayOfWeek.SATURDAY -> java.time.DayOfWeek.SATURDAY
            DayOfWeek.SUNDAY -> java.time.DayOfWeek.SUNDAY
        }

        // Calculate the date of the nth occurrence of this weekday in the month
        val firstDayOfMonth = date.withDayOfMonth(1)
        val firstOccurrence = firstDayOfMonth.with(TemporalAdjusters.nextOrSame(javaTimeDay))
        val targetDate = firstOccurrence.plusDays((weekOfMonth - 1) * 7L)

        // Check if the target date is in the same month and matches our date
        return targetDate.month == date.month && targetDate.dayOfMonth == date.dayOfMonth
    }
}
