package com.example.habits9.di

import com.example.habits9.data.CompletionRepository
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSectionRepository
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideFirebaseFirestore(): FirebaseFirestore {
        return FirebaseFirestore.getInstance()
    }

    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth {
        return FirebaseAuth.getInstance()
    }

    @Provides
    @Singleton
    fun provideHabitRepository(
        firestore: FirebaseFirestore,
        auth: FirebaseAuth
    ): HabitRepository {
        return HabitRepository(firestore, auth)
    }

    @Provides
    @Singleton
    fun provideHabitSectionRepository(
        firestore: FirebaseFirestore,
        auth: FirebaseAuth
    ): HabitSectionRepository {
        return HabitSectionRepository(firestore, auth)
    }

    @Provides
    @Singleton
    fun provideCompletionRepository(
        firestore: FirebaseFirestore,
        auth: FirebaseAuth
    ): CompletionRepository {
        return CompletionRepository(firestore, auth)
    }
}
