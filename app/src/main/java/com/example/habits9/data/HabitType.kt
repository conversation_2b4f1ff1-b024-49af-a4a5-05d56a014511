package com.example.habits9.data

enum class HabitType(val value: Int) {
    YES_NO(0), 
    NUMERICAL(1);

    companion object {
        fun fromInt(value: Int): HabitType {
            return when (value) {
                YES_NO.value -> YES_NO
                NUMERICAL.value -> NUMERICAL
                else -> throw IllegalStateException("Unknown habit type: $value")
            }
        }
    }
}

enum class NumericalHabitType(val value: Int) {
    AT_LEAST(0), 
    AT_MOST(1);

    companion object {
        fun fromInt(value: Int): NumericalHabitType {
            return when (value) {
                AT_LEAST.value -> AT_LEAST
                AT_MOST.value -> AT_MOST
                else -> throw IllegalStateException("Unknown numerical habit type: $value")
            }
        }
    }
}