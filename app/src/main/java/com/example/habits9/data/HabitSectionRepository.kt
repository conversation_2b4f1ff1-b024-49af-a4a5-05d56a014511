package com.example.habits9.data

import android.util.Log
import com.example.habits9.data.firestore.FirestoreConverters
import com.example.habits9.data.firestore.FirestoreHabitSection
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HabitSectionRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) {

    companion object {
        private const val TAG = "HabitSectionRepository"
        private const val USERS_COLLECTION = "users"
        private const val HABIT_SECTIONS_COLLECTION = "habitSections"
    }

    /**
     * Gets all habit sections for the current user using real-time listeners.
     */
    fun getAllHabitSections(): Flow<List<HabitSection>> = callbackFlow {
        val userId = auth.currentUser?.uid

        // Handle authentication gracefully without early return
        // Handle authentication gracefully without early return
        val listener: ListenerRegistration? = if (userId != null) {
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABIT_SECTIONS_COLLECTION)
                .orderBy("displayOrder")
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        Log.e(TAG, "Error listening to habit sections", error)
                        close(error)
                        return@addSnapshotListener
                    }

                    if (snapshot != null) {
                        val habitSections = snapshot.documents.mapNotNull { document ->
                            try {
                                val firestoreHabitSection = document.toObject(FirestoreHabitSection::class.java)
                                firestoreHabitSection?.let {
                                    val sectionWithId = it.copy(id = document.id)
                                    FirestoreConverters.firestoreToHabitSection(sectionWithId)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error converting document to habit section: ${document.id}", e)
                                null
                            }
                        }
                        trySend(habitSections)
                    } else {
                        trySend(emptyList())
                    }
                }
        } else {
            Log.w(TAG, "User not authenticated, returning empty habit sections list")
            trySend(emptyList())
            null
        }

        awaitClose { listener?.remove() }
    }

    /**
     * Gets all habit sections for the current user synchronously.
     * This is used for sorting operations where we need immediate access to section data.
     */
    suspend fun getAllSectionsSync(): List<HabitSection> {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty habit sections list")
            return emptyList()
        }

        return try {
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABIT_SECTIONS_COLLECTION)
                .orderBy("displayOrder")
                .get()
                .await()

            snapshot.documents.mapNotNull { document ->
                try {
                    val firestoreHabitSection = document.toObject(FirestoreHabitSection::class.java)
                    firestoreHabitSection?.let {
                        val sectionWithId = it.copy(id = document.id)
                        FirestoreConverters.firestoreToHabitSection(sectionWithId)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error converting document to habit section: ${document.id}", e)
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting habit sections synchronously", e)
            emptyList()
        }
    }

    /**
     * Inserts a new habit section for the current user.
     */
    suspend fun insertHabitSection(habitSection: HabitSection) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot insert habit section")
            throw IllegalStateException("User not authenticated")
        }

        try {
            val firestoreHabitSection = FirestoreConverters.habitSectionToFirestore(habitSection)

            val documentRef = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABIT_SECTIONS_COLLECTION)
                .add(firestoreHabitSection)
                .await()

            Log.d(TAG, "Habit section inserted with ID: ${documentRef.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting habit section", e)
            throw e
        }
    }

    /**
     * Updates an existing habit section for the current user.
     */
    suspend fun updateHabitSection(habitSection: HabitSection) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot update habit section")
            throw IllegalStateException("User not authenticated")
        }

        try {
            val firestoreHabitSection = FirestoreConverters.habitSectionToFirestore(habitSection)

            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABIT_SECTIONS_COLLECTION)
                .document(habitSection.id)
                .set(firestoreHabitSection)
                .await()

            Log.d(TAG, "Habit section updated: ${habitSection.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating habit section ${habitSection.id}", e)
            throw e
        }
    }

    /**
     * Updates multiple habit sections for the current user.
     */
    suspend fun updateHabitSections(habitSections: List<HabitSection>) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot update habit sections")
            throw IllegalStateException("User not authenticated")
        }

        try {
            val batch = firestore.batch()

            habitSections.forEach { habitSection ->
                val firestoreHabitSection = FirestoreConverters.habitSectionToFirestore(habitSection)
                val docRef = firestore
                    .collection(USERS_COLLECTION)
                    .document(userId)
                    .collection(HABIT_SECTIONS_COLLECTION)
                    .document(habitSection.id)

                batch.set(docRef, firestoreHabitSection)
            }

            batch.commit().await()
            Log.d(TAG, "Updated ${habitSections.size} habit sections")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating habit sections", e)
            throw e
        }
    }

    /**
     * Deletes a habit section for the current user.
     */
    suspend fun deleteHabitSection(habitSection: HabitSection) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete habit section")
            throw IllegalStateException("User not authenticated")
        }

        try {
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABIT_SECTIONS_COLLECTION)
                .document(habitSection.id)
                .delete()
                .await()

            Log.d(TAG, "Habit section deleted: ${habitSection.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting habit section ${habitSection.id}", e)
            throw e
        }
    }
}