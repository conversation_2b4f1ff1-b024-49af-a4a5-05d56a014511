package com.example.habits9.data.analytics

import android.util.Log
import com.example.habits9.data.HabitType

/**
 * Demonstration class showing how to use the analytics system.
 * This provides examples of how to integrate analytics into the UI layer.
 */
object AnalyticsDemo {

    private const val TAG = "AnalyticsDemo"

    /**
     * Example of how to use analytics in a ViewModel or UI component.
     */
    suspend fun demonstrateAnalyticsUsage(
        analyticsRepository: HabitAnalyticsRepository,
        habitId: Long,
        habitType: HabitType
    ) {
        Log.d(TAG, "=== Analytics Demo for Habit $habitId ===")

        try {
            // Get general information
            val generalInfo = analyticsRepository.getGeneralInfo()
            Log.d(TAG, "Current Week: ${generalInfo.currentWeek}")
            Log.d(TAG, "Current Date: ${generalInfo.currentDate}")

            when (habitType) {
                HabitType.YES_NO -> demonstrateYesNoAnalytics(analyticsRepository, habitId)
                HabitType.NUMERICAL -> demonstrateMeasurableAnalytics(analyticsRepository, habitId)
            }

            // Demonstrate chart data for different time periods
            demonstrateChartData(analyticsRepository, habitId)

            // Demonstrate calendar data
            demonstrateCalendarData(analyticsRepository, habitId, habitType)

        } catch (e: Exception) {
            Log.e(TAG, "Error in analytics demo", e)
        }
    }

    private suspend fun demonstrateYesNoAnalytics(
        analyticsRepository: HabitAnalyticsRepository,
        habitId: Long
    ) {
        Log.d(TAG, "--- Yes/No Habit Analytics ---")

        val analytics = analyticsRepository.getYesNoHabitAnalytics(habitId)

        Log.d(TAG, "Current Streak: ${analytics.currentStreak} days")
        Log.d(TAG, "Longest Streak: ${analytics.longestStreak} days")
        Log.d(TAG, "Completion Rate: ${String.format("%.1f", analytics.completionRate)}%")
        Log.d(TAG, "Total Completions: ${analytics.totalCompletions}")

        // Chart data
        Log.d(TAG, "Chart Data Points: ${analytics.completionHistory.size}")
        analytics.completionHistory.take(3).forEach { point ->
            Log.d(TAG, "  ${point.label}: ${point.value.toInt()} completions")
        }

        // Calendar data
        val completedDays = analytics.calendarData.count { it.value }
        val totalDays = analytics.calendarData.size
        Log.d(TAG, "Calendar: $completedDays/$totalDays days completed in last 6 months")
    }

    private suspend fun demonstrateMeasurableAnalytics(
        analyticsRepository: HabitAnalyticsRepository,
        habitId: Long
    ) {
        Log.d(TAG, "--- Measurable Habit Analytics ---")

        val analytics = analyticsRepository.getMeasurableHabitAnalytics(habitId)

        Log.d(TAG, "Current Streak: ${analytics.currentStreak} days")
        Log.d(TAG, "Longest Streak: ${analytics.longestStreak} days")
        Log.d(TAG, "Completion Rate: ${String.format("%.1f", analytics.completionRate)}%")
        Log.d(TAG, "Total Completions: ${analytics.totalCompletions}")
        Log.d(TAG, "Total Amount: ${String.format("%.2f", analytics.totalAmount)}")
        Log.d(TAG, "Average per Completion: ${String.format("%.2f", analytics.averagePerCompletion)}")
        Log.d(TAG, "Best Day: ${String.format("%.2f", analytics.bestDay)}")

        // Chart data
        Log.d(TAG, "Chart Data Points: ${analytics.completionHistory.size}")
        analytics.completionHistory.take(3).forEach { point ->
            Log.d(TAG, "  ${point.label}: ${String.format("%.2f", point.value)} total")
        }

        // Calendar data
        val activeDays = analytics.calendarData.count { it.value > 0 }
        val totalDays = analytics.calendarData.size
        val averageValue = analytics.calendarData.values.average()
        Log.d(TAG, "Calendar: $activeDays/$totalDays days active, avg: ${String.format("%.2f", averageValue)}")
    }

    private suspend fun demonstrateChartData(
        analyticsRepository: HabitAnalyticsRepository,
        habitId: Long
    ) {
        Log.d(TAG, "--- Chart Data for Different Time Periods ---")

        val timePeriods = listOf(
            TimePeriod.WEEK to "Weekly",
            TimePeriod.MONTH to "Monthly",
            TimePeriod.QUARTER to "Quarterly",
            TimePeriod.YEAR to "Yearly"
        )

        for ((period, label) in timePeriods) {
            val chartData = analyticsRepository.getCompletionHistory(habitId, period)
            Log.d(TAG, "$label data: ${chartData.size} points")
            chartData.take(2).forEach { point ->
                Log.d(TAG, "  ${point.label}: ${point.value}")
            }
        }
    }

    private suspend fun demonstrateCalendarData(
        analyticsRepository: HabitAnalyticsRepository,
        habitId: Long,
        habitType: HabitType
    ) {
        Log.d(TAG, "--- Calendar Heatmap Data ---")

        val calendarData = analyticsRepository.getCalendarData(habitId)
        Log.d(TAG, "Calendar data covers ${calendarData.size} days")

        when (habitType) {
            HabitType.YES_NO -> {
                val completedDays = calendarData.count { (_, value) -> value as Boolean }
                Log.d(TAG, "Completed days: $completedDays")
            }
            HabitType.NUMERICAL -> {
                val activeDays = calendarData.count { (_, value) -> (value as Float) > 0 }
                val totalValue = calendarData.values.sumOf { (it as Float).toDouble() }
                Log.d(TAG, "Active days: $activeDays, Total value: ${String.format("%.2f", totalValue)}")
            }
        }
    }

    /**
     * Example of individual metric access.
     */
    suspend fun demonstrateIndividualMetrics(
        analyticsRepository: HabitAnalyticsRepository,
        habitId: Long
    ) {
        Log.d(TAG, "=== Individual Metrics Demo ===")

        val currentStreak = analyticsRepository.getCurrentStreak(habitId)
        val longestStreak = analyticsRepository.getLongestStreak(habitId)
        val completionRate = analyticsRepository.getCompletionRate(habitId)
        val totalCompletions = analyticsRepository.getTotalCompletions(habitId)

        Log.d(TAG, "Current Streak: $currentStreak")
        Log.d(TAG, "Longest Streak: $longestStreak")
        Log.d(TAG, "Completion Rate: ${String.format("%.1f", completionRate)}%")
        Log.d(TAG, "Total Completions: $totalCompletions")

        // For measurable habits only
        val totalAmount = analyticsRepository.getTotalAmount(habitId)
        val averagePerCompletion = analyticsRepository.getAveragePerCompletion(habitId)
        val bestDay = analyticsRepository.getBestDay(habitId)

        if (totalAmount > 0) {
            Log.d(TAG, "Total Amount: ${String.format("%.2f", totalAmount)}")
            Log.d(TAG, "Average per Completion: ${String.format("%.2f", averagePerCompletion)}")
            Log.d(TAG, "Best Day: ${String.format("%.2f", bestDay)}")
        }
    }
}
