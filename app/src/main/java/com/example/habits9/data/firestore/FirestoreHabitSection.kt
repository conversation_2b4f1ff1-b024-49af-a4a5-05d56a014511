package com.example.habits9.data.firestore

/**
 * Firestore data model for habit sections.
 * This is a simple POJO without Room annotations for Firestore serialization/deserialization.
 */
data class FirestoreHabitSection(
    val id: String = "", // Firestore document ID
    val name: String = "",
    val color: Int = 0,
    val displayOrder: Int = 0
) {
    // No-argument constructor required by Firestore
    constructor() : this(
        id = "",
        name = "",
        color = 0,
        displayOrder = 0
    )
}
