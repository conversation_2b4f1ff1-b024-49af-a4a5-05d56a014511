package com.example.habits9.data

/**
 * Represents a habit completion record.
 * This entity stores when a user completes a habit and the value for measurable habits.
 */
data class Completion(
    val id: String = "", // Firestore document ID as string for proper deletion

    /** The ID of the habit this completion belongs to */
    val habitId: Long,

    /** The date of completion as Unix timestamp (milliseconds) */
    val timestamp: Long,

    /**
     * The value for measurable habits (e.g., "5", "1.5").
     * Should be null for Yes/No habits.
     */
    val value: String? = null
)