package com.example.habits9.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

// Extension property to create DataStore instance
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_preferences")

/**
 * Enum representing different habit sort types
 */
enum class HabitSortType {
    BY_NAME,
    BY_SECTION,
    CUSTOM_ORDER
}

@Singleton
class UserPreferencesRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private object PreferencesKeys {
        val FIRST_DAY_OF_WEEK = stringPreferencesKey("first_day_of_week")
        val HABIT_SORT_TYPE = stringPreferencesKey("habit_sort_type")
        val CUSTOM_HABIT_ORDER = stringPreferencesKey("custom_habit_order")
    }
    
    /**
     * Get the first day of week preference as a Flow
     * @return Flow<String> - "SUNDAY" or "MONDAY", defaults to "SUNDAY"
     */
    val firstDayOfWeek: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[PreferencesKeys.FIRST_DAY_OF_WEEK] ?: "SUNDAY"
    }

    /**
     * Get the habit sort type preference as a Flow
     * @return Flow<HabitSortType> - defaults to CUSTOM_ORDER
     */
    val habitSortType: Flow<HabitSortType> = context.dataStore.data.map { preferences ->
        val sortTypeString = preferences[PreferencesKeys.HABIT_SORT_TYPE] ?: HabitSortType.CUSTOM_ORDER.name
        try {
            HabitSortType.valueOf(sortTypeString)
        } catch (e: IllegalArgumentException) {
            HabitSortType.CUSTOM_ORDER
        }
    }

    /**
     * Get the custom habit order as a Flow
     * @return Flow<List<String>> - list of habit UUIDs in custom order
     */
    val customHabitOrder: Flow<List<String>> = context.dataStore.data.map { preferences ->
        val orderString = preferences[PreferencesKeys.CUSTOM_HABIT_ORDER] ?: ""
        if (orderString.isBlank()) {
            emptyList()
        } else {
            orderString.split(",").filter { it.isNotBlank() }
        }
    }

    /**
     * Update the first day of week preference
     * @param dayOfWeek String - "SUNDAY" or "MONDAY"
     */
    suspend fun updateFirstDayOfWeek(dayOfWeek: String) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.FIRST_DAY_OF_WEEK] = dayOfWeek
        }
    }

    /**
     * Update the habit sort type preference
     * @param sortType HabitSortType - the sort type to set
     */
    suspend fun updateHabitSortType(sortType: HabitSortType) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.HABIT_SORT_TYPE] = sortType.name
        }
    }

    /**
     * Update the custom habit order preference
     * @param habitUuids List<String> - list of habit UUIDs in desired order
     */
    suspend fun updateCustomHabitOrder(habitUuids: List<String>) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.CUSTOM_HABIT_ORDER] = habitUuids.joinToString(",")
        }
    }
}