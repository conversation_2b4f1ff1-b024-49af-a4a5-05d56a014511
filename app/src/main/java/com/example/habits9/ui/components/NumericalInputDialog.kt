package com.example.habits9.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.habits9.ui.home.AccentPrimary
import com.example.habits9.ui.home.BackgroundDark
import com.example.habits9.ui.home.DividerColor
import com.example.habits9.ui.home.SurfaceVariantDark
import com.example.habits9.ui.home.TextPrimary
import com.example.habits9.ui.home.TextSecondary

/**
 * A dialog for inputting numerical values for measurable habits.
 * Matches the design in 24.jpg with habit name, date, target display, and numerical input.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NumericalInputDialog(
    isVisible: Boolean,
    habitName: String,
    unit: String,
    currentValue: String,
    targetValue: Double = 0.0,
    targetType: String = "at least",
    onValueChange: (String) -> Unit,
    onConfirm: (Double) -> Unit,
    onDismiss: () -> Unit
) {
    android.util.Log.d("BugFix", "NumericalInputDialog - isVisible: $isVisible, habitName: $habitName, unit: $unit")

    if (isVisible) {
        android.util.Log.d("BugFix", "NumericalInputDialog - Rendering dialog")
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            val focusRequester = remember { FocusRequester() }
            val keyboardController = LocalSoftwareKeyboardController.current
            
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = SurfaceVariantDark
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Habit name
                    Text(
                        text = habitName,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = TextPrimary,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Date (Today)
                    Text(
                        text = "Today",
                        color = TextSecondary,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Target display
                    val targetText = if (unit.isNotEmpty()) {
                        "Target: $targetType ${targetValue.toInt()} $unit"
                    } else {
                        "Target: $targetType ${targetValue.toInt()}"
                    }
                    Text(
                        text = targetText,
                        color = TextSecondary,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Input field
                    OutlinedTextField(
                        value = currentValue,
                        onValueChange = onValueChange,
                        label = {
                            Text(
                                text = if (unit.isNotEmpty()) "Value ($unit)" else "Value",
                                color = TextSecondary
                            )
                        },
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Decimal,
                            imeAction = ImeAction.Done
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = {
                                keyboardController?.hide()
                                val value = currentValue.toDoubleOrNull()
                                if (value != null && value >= 0) {
                                    onConfirm(value)
                                }
                            }
                        ),
                        singleLine = true,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = AccentPrimary,
                            unfocusedBorderColor = DividerColor,
                            focusedLabelColor = AccentPrimary,
                            unfocusedLabelColor = TextSecondary,
                            focusedTextColor = TextPrimary,
                            unfocusedTextColor = TextPrimary,
                            cursorColor = AccentPrimary
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester)
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Cancel button
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = TextSecondary
                            ),
                            border = BorderStroke(
                                width = 1.dp,
                                color = TextSecondary
                            )
                        ) {
                            Text(
                                text = "Cancel",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        // OK button
                        Button(
                            onClick = {
                                val value = currentValue.toDoubleOrNull()
                                if (value != null && value >= 0) {
                                    onConfirm(value)
                                } else {
                                    // Show error or handle invalid input
                                    onDismiss()
                                }
                            },
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = AccentPrimary,
                                contentColor = BackgroundDark
                            )
                        ) {
                            Text(
                                text = "OK",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
            
            // Request focus when dialog appears
            LaunchedEffect(Unit) {
                focusRequester.requestFocus()
            }
        }
    }
}
