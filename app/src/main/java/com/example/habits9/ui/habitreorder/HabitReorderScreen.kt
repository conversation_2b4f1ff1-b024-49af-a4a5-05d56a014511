package com.example.habits9.ui.habitreorder

import com.example.habits9.data.Habit
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateOffsetAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.zIndex
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.compositeOver
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.foundation.isSystemInDarkTheme
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitReorderScreen(
    onBackClick: () -> Unit,
    viewModel: HabitReorderViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val hapticFeedback = LocalHapticFeedback.current

    // REMOVED SAVE BUTTON: Changes now persist immediately on drop
    val handleBackClick = {
        // Just navigate back - no need to save or discard since changes are immediate
        onBackClick()
    }

    // FIX: Revert to index-based tracking like working ManageSectionsScreen
    var draggedItemIndex by remember { mutableStateOf(-1) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var targetDropIndex by remember { mutableStateOf(-1) }
    var isDragging by remember { mutableStateOf(false) }
    var isSettling by remember { mutableStateOf(false) }  // New state for settlement animation
    val listState = rememberLazyListState()

    // Track the original dragged item for continuous dragging
    var originalDraggedItem by remember { mutableStateOf<Habit?>(null) }

    // FORCE UI REFRESH: Add refresh trigger to force recomposition after drag operations
    var refreshTrigger by remember { mutableStateOf(0) }

    // Force refresh function
    val forceRefresh = {
        refreshTrigger++
        android.util.Log.d("HabitReorderScreen", "FORCE REFRESH: trigger=$refreshTrigger")
    }

    // Smooth animated drag offset for better finger tracking
    val animatedDragOffset by animateOffsetAsState(
        targetValue = dragOffset,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioNoBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "dragOffset"
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Reorder Habits",
                        color = MaterialTheme.colorScheme.onSurface,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = handleBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                },
                // REMOVED: Save button - changes now persist immediately
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            )
        },
        containerColor = MaterialTheme.colorScheme.background
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.habits.isEmpty()) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "You have no habits to organize.",
                        color = MaterialTheme.colorScheme.onSurface,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Go back and add a new habit to get started!",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 14.sp
                    )
                }
            } else {
                // Habit list
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    itemsIndexed(
                        items = uiState.habits,
                        key = { index, habit -> "${habit.uuid}_${refreshTrigger}" }  // FORCE REFRESH: Include trigger in key
                    ) { index, habit ->
                        val isDraggingThisItem = index == draggedItemIndex

                        // NEW LOGIC: Simple one-position shift as user described
                        val displacement = when {
                            isDraggingThisItem -> 0f  // Dragged item follows finger
                            isDragging && draggedItemIndex != -1 && targetDropIndex != -1 -> {
                                when {
                                    // Moving UP: Cards between original and target move DOWN one position
                                    // Example: Card A (pos 4→3), card at pos 3 moves to pos 4
                                    draggedItemIndex > targetDropIndex && index >= targetDropIndex && index < draggedItemIndex -> 1f

                                    // Moving DOWN: Cards between original and target move UP one position
                                    // Example: Card A (pos 1→2), card at pos 2 moves to pos 1
                                    draggedItemIndex < targetDropIndex && index > draggedItemIndex && index <= targetDropIndex -> -1f

                                    else -> 0f
                                }
                            }
                            else -> 0f
                        }

                        val animatedDisplacement by animateFloatAsState(
                            targetValue = displacement,
                            animationSpec = tween(durationMillis = 150), // Faster for immediate feedback
                            label = "displacement_$index"
                        )

                        // Debug logging for new displacement logic
                        if (displacement != 0f) {
                            android.util.Log.d("HabitReorderScreen", "NEW LOGIC - Item $index (${habit.name}) displacement: $displacement, dragged: $draggedItemIndex -> $targetDropIndex")
                        }

                        HabitReorderItem(
                            habitName = habit.name,
                            habitUuid = habit.uuid,  // FIX: Pass UUID for stable gesture handling
                            isDragging = isDraggingThisItem,
                            isSettling = isSettling && isDraggingThisItem,  // Pass settling state
                            dragOffset = if (isDraggingThisItem) animatedDragOffset else Offset.Zero,
                            isDropTarget = targetDropIndex == index && !isDraggingThisItem,
                            displacement = animatedDisplacement,
                            onDragStart = {
                                draggedItemIndex = index  // FIX: Use index like working ManageSectionsScreen
                                originalDraggedItem = habit  // Store the original item being dragged
                                dragOffset = Offset.Zero
                                targetDropIndex = -1
                                isDragging = true
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            },
                            onDrag = { dragAmount ->
                                dragOffset += dragAmount

                                // Enhanced drop target calculation for fluid finger tracking
                                val visibleItems = listState.layoutInfo.visibleItemsInfo
                                val currentItemInfo = visibleItems.find { it.index == draggedItemIndex }

                                if (currentItemInfo != null) {
                                    val itemCenterY = currentItemInfo.offset + currentItemInfo.size / 2 + dragOffset.y

                                    // Find the item we're hovering over
                                    var hoveredItem: androidx.compose.foundation.lazy.LazyListItemInfo? = null
                                    for (item in visibleItems) {
                                        if (item.index == draggedItemIndex) continue

                                        val itemTop = item.offset
                                        val itemBottom = item.offset + item.size

                                        if (itemCenterY >= itemTop && itemCenterY <= itemBottom) {
                                            hoveredItem = item
                                            break
                                        }
                                    }

                                    // Calculate the correct insertion position
                                    val newTargetIndex = if (hoveredItem != null) {
                                        val itemTop = hoveredItem.offset
                                        val itemBottom = hoveredItem.offset + hoveredItem.size
                                        val itemMiddle = itemTop + hoveredItem.size / 2

                                        // Determine insertion position based on which half we're in
                                        if (itemCenterY <= itemMiddle) {
                                            // Top half - insert before this item
                                            hoveredItem.index
                                        } else {
                                            // Bottom half - insert after this item
                                            hoveredItem.index + 1
                                        }
                                    } else -1

                                    // Update target drop index for visual feedback only
                                    if (newTargetIndex != targetDropIndex) {
                                        targetDropIndex = newTargetIndex
                                        if (newTargetIndex != -1) {
                                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        }
                                    }

                                    // DO NOT update data model during drag - this was the root cause
                                    // Only track visual feedback, actual reorder happens on drag end
                                }
                            },
                            onDragEnd = {
                                // Store values before reset
                                val fromIndex = draggedItemIndex
                                val toIndex = targetDropIndex

                                // NEW LOGIC: Immediate state reset for clean UI
                                isDragging = false
                                draggedItemIndex = -1
                                originalDraggedItem = null
                                dragOffset = Offset.Zero
                                targetDropIndex = -1
                                isSettling = false

                                // NEW LOGIC: Immediate data update as user requested
                                if (toIndex != -1 && fromIndex != -1 && toIndex != fromIndex) {
                                    android.util.Log.d("HabitReorderScreen", "onDragEnd: Moving from $fromIndex to $toIndex - IMMEDIATE UPDATE")

                                    // Immediate backend update - no local state, direct to backend
                                    viewModel.moveHabitAndSave(fromIndex, toIndex)

                                    // FORCE UI REFRESH: Trigger immediate recomposition after data update
                                    forceRefresh()

                                    // FORCE VIEWMODEL REFRESH: Also force ViewModel to reload from repository after a brief delay
                                    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                                        kotlinx.coroutines.delay(100) // Small delay to ensure backend update completes
                                        viewModel.forceRefresh()
                                    }
                                } else {
                                    android.util.Log.d("HabitReorderScreen", "onDragEnd: No move - targetDropIndex=$toIndex, draggedItemIndex=$fromIndex")
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun HabitReorderItem(
    habitName: String,
    habitUuid: String,  // FIX: Add UUID for stable gesture handling
    isDragging: Boolean,
    isSettling: Boolean = false,  // New parameter for settling state
    dragOffset: Offset,
    isDropTarget: Boolean = false,
    displacement: Float = 0f,
    onDragStart: () -> Unit,
    onDrag: (Offset) -> Unit,
    onDragEnd: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Enhanced visual feedback with precise timing and settling animation
    val animatedElevation by animateDpAsState(
        targetValue = when {
            isDragging -> 16.dp  // Noticeable elevation shadow for lift effect
            isSettling -> 2.dp   // Animate back to normal during settling
            isDropTarget -> 8.dp
            else -> 2.dp
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Slightly longer for smooth settlement
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // 150ms as specified in prompt
                easing = FastOutSlowInEasing
            )
        },
        label = "elevation"
    )

    val animatedScale by animateFloatAsState(
        targetValue = when {
            isDragging -> 1.05f  // Exactly 1.05x as specified in prompt
            isSettling -> 1f     // Animate back to normal scale during settling
            isDropTarget -> 1.02f
            else -> 1f
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Smooth settlement animation
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // 150ms as specified in prompt
                easing = FastOutSlowInEasing
            )
        },
        label = "scale"
    )

    // SIMPLIFIED: No alpha animation - all items stay fully visible
    val animatedAlpha = if (isDragging) 0.95f else 1f

    // Enhanced background color animation with Material3 surface-variant support
    val animatedBackgroundColor by animateColorAsState(
        targetValue = when {
            isDragging -> MaterialTheme.colorScheme.surfaceVariant  // Use Material3 surface-variant color
            isSettling -> MaterialTheme.colorScheme.surfaceVariant  // Return to normal during settling
            isDropTarget -> MaterialTheme.colorScheme.primary.copy(alpha = 0.08f).compositeOver(MaterialTheme.colorScheme.surfaceVariant)
            else -> MaterialTheme.colorScheme.surfaceVariant
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Smooth color transition during settlement
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // 150ms as specified in prompt
                easing = FastOutSlowInEasing
            )
        },
        label = "backgroundColor"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = animatedElevation,
                shape = RoundedCornerShape(12.dp)
            )
            .graphicsLayer {
                // FIX: Include displacement for space-making animation
                translationX = dragOffset.x
                translationY = dragOffset.y + (displacement * 80.dp.toPx())
                scaleX = animatedScale
                scaleY = animatedScale
                alpha = animatedAlpha
            }
            // FIX: Proper z-ordering using zIndex modifier (outside graphicsLayer)
            .zIndex(if (isDragging) 10f else if (isDropTarget) 5f else 1f)
            // FIX: Make entire card draggable with long press, like the reference implementation
            .pointerInput(habitUuid) {  // FIX: Use UUID for stable gesture handling
                detectDragGesturesAfterLongPress(
                    onDragStart = { offset ->
                        onDragStart()
                    },
                    onDrag = { change, dragAmount ->
                        change.consume()
                        onDrag(dragAmount)
                    },
                    onDragEnd = {
                        onDragEnd()
                    },
                    onDragCancel = {
                        onDragEnd()
                    }
                )
            },
        colors = CardDefaults.cardColors(
            containerColor = animatedBackgroundColor  // Use animated background color
        ),
        shape = RoundedCornerShape(12.dp),
        border = when {
            isDragging -> BorderStroke(2.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.4f))
            isDropTarget -> BorderStroke(1.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.2f))
            else -> null
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // FIX: Remove drag handle icon - entire card is now draggable
            Text(
                text = habitName,
                color = when {
                    isDragging -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.95f)
                    isDropTarget -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                    else -> MaterialTheme.colorScheme.onSurface
                },
                fontSize = 16.sp,
                fontWeight = when {
                    isDragging -> FontWeight.SemiBold
                    isDropTarget -> FontWeight.Medium
                    else -> FontWeight.Medium
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}
