package com.example.habits9.ui.details.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.TextStyle
import com.example.habits9.data.analytics.ChartDataPoint
import com.example.habits9.data.analytics.TimePeriod
import kotlin.math.max

/**
 * Completion History Chart component for habit analytics.
 * Displays completion data as a bar chart with time period filtering.
 */
@Composable
fun CompletionHistoryChart(
    chartData: List<ChartDataPoint>,
    selectedTimePeriod: TimePeriod,
    onTimePeriodChanged: (TimePeriod) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var expanded by remember { mutableStateOf(false) }
    
    // Theme colors
    val backgroundColor = MaterialTheme.colorScheme.surface
    val textColor = MaterialTheme.colorScheme.onSurface
    val accentColor = MaterialTheme.colorScheme.primary
    val secondaryTextColor = MaterialTheme.colorScheme.onSurfaceVariant

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp) // --padding-card from style guide
        ) {
            // Header with title and time period filter
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Completion History",
                    color = textColor,
                    fontSize = 14.sp, // display-small from style guide
                    fontFamily = FontFamily.Default, // Inter font family
                    fontWeight = FontWeight.SemiBold, // 600 weight from style guide
                    letterSpacing = (-0.01).sp // -0.01em from style guide
                )
                
                // Time period dropdown
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = selectedTimePeriod.name.lowercase().replaceFirstChar { it.uppercase() },
                        onValueChange = { },
                        readOnly = true,
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .width(100.dp)
                            .height(40.dp),
                        textStyle = androidx.compose.ui.text.TextStyle(
                            fontSize = 10.sp, // label-small from style guide
                            fontFamily = FontFamily.Monospace, // Roboto Mono from style guide
                            color = secondaryTextColor,
                            letterSpacing = 0.05.sp // 0.05em from style guide
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = accentColor,
                            unfocusedBorderColor = secondaryTextColor
                        )
                    )
                    
                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        TimePeriod.values().forEach { period ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = period.name.lowercase().replaceFirstChar { it.uppercase() },
                                        fontSize = 10.sp, // label-small from style guide
                                        fontFamily = FontFamily.Monospace, // Roboto Mono from style guide
                                        color = textColor,
                                        letterSpacing = 0.05.sp // 0.05em from style guide
                                    )
                                },
                                onClick = {
                                    onTimePeriodChanged(period)
                                    expanded = false
                                }
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp)) // space-lg from style guide
            
            // Chart view
            if (chartData.isNotEmpty()) {
                ComposeBarChart(
                    data = chartData,
                    accentColor = accentColor,
                    textColor = textColor,
                    secondaryTextColor = secondaryTextColor,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(220.dp)
                )
            } else {
                // Empty state
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(220.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        color = secondaryTextColor,
                        fontSize = 12.sp,
                        fontFamily = FontFamily.Default
                    )
                }
            }
        }
    }
}

@Composable
private fun ComposeBarChart(
    data: List<ChartDataPoint>,
    accentColor: Color,
    textColor: Color,
    secondaryTextColor: Color,
    modifier: Modifier = Modifier
) {
    val textMeasurer = rememberTextMeasurer()

    Canvas(
        modifier = modifier
            .background(Color.Transparent)
            .border(
                width = 1.dp,
                color = secondaryTextColor.copy(alpha = 0.2f),
                shape = RoundedCornerShape(4.dp)
            )
            .padding(16.dp)
    ) {
        if (data.isEmpty()) return@Canvas

        val maxValue = data.maxOfOrNull { it.value } ?: 1f
        val barWidth = (size.width - 40.dp.toPx()) / data.size
        val chartHeight = size.height - 60.dp.toPx()

        // Draw bars
        data.forEachIndexed { index, dataPoint ->
            val barHeight = (dataPoint.value / maxValue) * chartHeight
            val x = 20.dp.toPx() + index * barWidth + barWidth * 0.2f
            val y = size.height - 40.dp.toPx() - barHeight
            val width = barWidth * 0.6f

            // Draw bar
            drawRect(
                color = accentColor,
                topLeft = Offset(x, y),
                size = Size(width, barHeight)
            )

            // Draw value text above bar
            val valueText = if (dataPoint.value % 1 == 0f) {
                dataPoint.value.toInt().toString()
            } else {
                String.format("%.1f", dataPoint.value)
            }

            drawText(
                textMeasurer = textMeasurer,
                text = valueText,
                topLeft = Offset(
                    x + width / 2 - textMeasurer.measure(valueText).size.width / 2,
                    y - 20.dp.toPx()
                ),
                style = TextStyle(
                    color = textColor,
                    fontSize = 8.sp,
                    fontFamily = FontFamily.Monospace
                )
            )

            // Draw label below chart - Show every 3rd label to prevent overlap
            // This improves readability when there are many data points
            val shouldShowLabel = when {
                data.size <= 7 -> true // Show all labels for small datasets
                data.size <= 14 -> index % 2 == 0 // Show every 2nd label for medium datasets
                else -> index % 3 == 0 // Show every 3rd label for large datasets
            }

            if (shouldShowLabel) {
                val labelText = dataPoint.label
                drawText(
                    textMeasurer = textMeasurer,
                    text = labelText,
                    topLeft = Offset(
                        x + width / 2 - textMeasurer.measure(labelText).size.width / 2,
                        size.height - 30.dp.toPx()
                    ),
                    style = TextStyle(
                        color = secondaryTextColor,
                        fontSize = 8.sp,
                        fontFamily = FontFamily.Monospace
                    )
                )
            }
        }

        // Draw horizontal grid lines
        for (i in 1..4) {
            val y = size.height - 40.dp.toPx() - (chartHeight * i / 4)
            drawLine(
                color = secondaryTextColor.copy(alpha = 0.3f),
                start = Offset(20.dp.toPx(), y),
                end = Offset(size.width - 20.dp.toPx(), y),
                strokeWidth = 1.dp.toPx()
            )
        }
    }
}
