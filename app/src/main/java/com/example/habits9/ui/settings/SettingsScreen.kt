package com.example.habits9.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.firebase.auth.EmailAuthProvider
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthRecentLoginRequiredException

// Design System Colors - Dark Theme (from style guide)
private val DarkBackground = Color(0xFF121826) // background
private val SurfaceVariantDark = Color(0xFF1A202C) // surface-variant
private val TextPrimary = Color(0xFFE2E8F0) // text-primary
private val TextSecondary = Color(0xFFA0AEC0) // text-secondary
private val AccentPrimary = Color(0xFF81E6D9) // accent-primary

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit = {},
    onSignOut: () -> Unit = {},
    settingsViewModel: SettingsViewModel = hiltViewModel()
) {
    val firstDayOfWeek by settingsViewModel.firstDayOfWeek.collectAsState()
    var showDeleteAccountDialog by remember { mutableStateOf(false) }
    var showReauthDialog by remember { mutableStateOf(false) }
    var isDeleting by remember { mutableStateOf(false) }
    
    Scaffold(
        containerColor = DarkBackground,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Settings",
                        color = TextPrimary,
                        fontSize = 18.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark,
                    titleContentColor = TextPrimary,
                    navigationIconContentColor = TextPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 20.dp) // viewport spacing from style guide
        ) {
            Spacer(modifier = Modifier.height(16.dp)) // section spacing
            
            // First day of week setting section
            Text(
                text = "First day of week",
                color = TextPrimary,
                fontSize = 16.sp,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // Radio button group
            Column(
                modifier = Modifier.selectableGroup()
            ) {
                // Sunday option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (firstDayOfWeek == "SUNDAY"),
                            onClick = { settingsViewModel.updateFirstDayOfWeek("SUNDAY") },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 8.dp), // compact spacing
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (firstDayOfWeek == "SUNDAY"),
                        onClick = null, // handled by Row's selectable
                        colors = RadioButtonDefaults.colors(
                            selectedColor = AccentPrimary,
                            unselectedColor = TextSecondary
                        )
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Sunday",
                        color = TextPrimary,
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Normal
                    )
                }
                
                // Monday option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (firstDayOfWeek == "MONDAY"),
                            onClick = { settingsViewModel.updateFirstDayOfWeek("MONDAY") },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 8.dp), // compact spacing
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (firstDayOfWeek == "MONDAY"),
                        onClick = null, // handled by Row's selectable
                        colors = RadioButtonDefaults.colors(
                            selectedColor = AccentPrimary,
                            unselectedColor = TextSecondary
                        )
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "Monday",
                        color = TextPrimary,
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Normal
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp)) // larger spacing before destructive actions

            // Delete Account Button
            Button(
                onClick = { showDeleteAccountDialog = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp), // minimum touch target from style guide
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFDC2626), // Red color for destructive action
                    contentColor = Color.White
                ),
                enabled = !isDeleting
            ) {
                if (isDeleting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(
                        "Delete Account",
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Sign Out Button
            Button(
                onClick = {
                    FirebaseAuth.getInstance().signOut()
                    onSignOut()
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp), // minimum touch target from style guide
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF718096), // Secondary color for sign out
                    contentColor = Color.White
                )
            ) {
                Text(
                    "Sign Out",
                    fontSize = 14.sp,
                    fontFamily = FontFamily.Default,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }

    // Delete Account Confirmation Dialog
    if (showDeleteAccountDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAccountDialog = false },
            title = {
                Text(
                    text = "Delete Account",
                    color = TextPrimary,
                    style = androidx.compose.material3.MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    )
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to permanently delete your account? All of your data will be lost. This action cannot be undone.",
                    color = TextSecondary,
                    style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        showDeleteAccountDialog = false
                        deleteAccount(
                            onSuccess = onSignOut,
                            onReauthRequired = { showReauthDialog = true },
                            onError = { /* Handle error */ }
                        )
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFDC2626),
                        contentColor = Color.White
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteAccountDialog = false }
                ) {
                    Text(
                        "Cancel",
                        color = TextSecondary
                    )
                }
            }
        )
    }

    // Reauthentication Dialog
    if (showReauthDialog) {
        ReauthenticationDialog(
            onDismiss = { showReauthDialog = false },
            onReauthenticate = { password ->
                reauthenticateAndDelete(
                    password = password,
                    onSuccess = {
                        showReauthDialog = false
                        onSignOut()
                    },
                    onError = { /* Handle error */ },
                    setLoading = { isDeleting = it }
                )
            }
        )
    }
}

private fun deleteAccount(
    onSuccess: () -> Unit,
    onReauthRequired: () -> Unit,
    onError: (String) -> Unit
) {
    val user = FirebaseAuth.getInstance().currentUser
    user?.delete()?.addOnCompleteListener { task ->
        if (task.isSuccessful) {
            onSuccess()
        } else {
            val exception = task.exception
            if (exception is FirebaseAuthRecentLoginRequiredException) {
                onReauthRequired()
            } else {
                onError(exception?.message ?: "Failed to delete account")
            }
        }
    }
}

private fun reauthenticateAndDelete(
    password: String,
    onSuccess: () -> Unit,
    onError: (String) -> Unit,
    setLoading: (Boolean) -> Unit
) {
    setLoading(true)
    val user = FirebaseAuth.getInstance().currentUser
    val email = user?.email

    if (email != null) {
        val credential = EmailAuthProvider.getCredential(email, password)
        user.reauthenticate(credential).addOnCompleteListener { reauthTask ->
            if (reauthTask.isSuccessful) {
                user.delete().addOnCompleteListener { deleteTask ->
                    setLoading(false)
                    if (deleteTask.isSuccessful) {
                        onSuccess()
                    } else {
                        onError(deleteTask.exception?.message ?: "Failed to delete account")
                    }
                }
            } else {
                setLoading(false)
                onError(reauthTask.exception?.message ?: "Authentication failed")
            }
        }
    } else {
        setLoading(false)
        onError("User email not found")
    }
}

@Composable
private fun ReauthenticationDialog(
    onDismiss: () -> Unit,
    onReauthenticate: (String) -> Unit
) {
    var password by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Confirm Identity",
                color = TextPrimary,
                style = androidx.compose.material3.MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Medium
                )
            )
        },
        text = {
            Column {
                Text(
                    text = "Please enter your password to confirm account deletion.",
                    color = TextSecondary,
                    style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    placeholder = {
                        Text(
                            "Password",
                            style = androidx.compose.material3.MaterialTheme.typography.bodyMedium,
                            color = TextSecondary
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Password",
                            tint = Color(0xFFA0AEC0)
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp)),
                    visualTransformation = PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,
                    enabled = !isLoading,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = Color.Transparent,
                        focusedContainerColor = SurfaceVariantDark,
                        unfocusedContainerColor = SurfaceVariantDark,
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary
                    )
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (password.isNotBlank()) {
                        isLoading = true
                        onReauthenticate(password)
                    }
                },
                enabled = !isLoading && password.isNotBlank(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFDC2626),
                    contentColor = Color.White
                )
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("Confirm Delete")
                }
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text(
                    "Cancel",
                    color = TextSecondary
                )
            }
        }
    )
}