package com.example.habits9.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.habits9.data.HabitSortType

// Colors from style guide
private val BackgroundDark = Color(0xFF121826)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val DividerColor = Color(0xFF2D3748)

@Composable
fun SortMenuButton(
    currentSortType: HabitSortType,
    onSortTypeSelected: (HabitSortType) -> Unit,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    
    Box(modifier = modifier) {
        // Sort icon button
        IconButton(
            onClick = { showMenu = true },
            modifier = Modifier
                .size(40.dp)
                .background(
                    color = SurfaceVariantDark,
                    shape = RoundedCornerShape(8.dp)
                )
        ) {
            Icon(
                imageVector = Icons.Default.Sort,
                contentDescription = "Sort habits",
                tint = TextPrimary,
                modifier = Modifier.size(20.dp)
            )
        }
        
        // Dropdown menu
        DropdownMenu(
            expanded = showMenu,
            onDismissRequest = { showMenu = false },
            modifier = Modifier
                .background(
                    color = SurfaceVariantDark,
                    shape = RoundedCornerShape(8.dp)
                )
                .width(200.dp)
        ) {
            SortMenuItem(
                text = "Sort by Name",
                isSelected = currentSortType == HabitSortType.BY_NAME,
                onClick = {
                    onSortTypeSelected(HabitSortType.BY_NAME)
                    showMenu = false
                }
            )
            
            Divider(
                color = DividerColor,
                thickness = 1.dp,
                modifier = Modifier.padding(horizontal = 8.dp)
            )
            
            SortMenuItem(
                text = "Sort by Section",
                isSelected = currentSortType == HabitSortType.BY_SECTION,
                onClick = {
                    onSortTypeSelected(HabitSortType.BY_SECTION)
                    showMenu = false
                }
            )
            
            Divider(
                color = DividerColor,
                thickness = 1.dp,
                modifier = Modifier.padding(horizontal = 8.dp)
            )
            
            SortMenuItem(
                text = "Custom Order (Drag & Drop)",
                isSelected = currentSortType == HabitSortType.CUSTOM_ORDER,
                onClick = {
                    onSortTypeSelected(HabitSortType.CUSTOM_ORDER)
                    showMenu = false
                }
            )
        }
    }
}

@Composable
private fun SortMenuItem(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            color = TextPrimary,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = AccentPrimary,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}
