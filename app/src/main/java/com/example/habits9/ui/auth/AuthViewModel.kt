package com.example.habits9.ui.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject

data class AuthUiState(
    val email: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String = "",
    val isSignedIn: Boolean = false,
    val isEmailVerified: Boolean = false
)

@HiltViewModel
class AuthViewModel @Inject constructor() : ViewModel() {
    
    private val firebaseAuth = FirebaseAuth.getInstance()
    
    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()
    
    init {
        // Check if user is already signed in
        checkAuthState()
    }
    
    private fun checkAuthState() {
        val currentUser = firebaseAuth.currentUser
        _uiState.value = _uiState.value.copy(
            isSignedIn = currentUser != null
        )
    }
    
    fun updateEmail(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            errorMessage = "" // Clear error when user types
        )
    }
    
    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            errorMessage = "" // Clear error when user types
        )
    }
    
    fun signUp(onSuccess: (String) -> Unit) {
        val currentState = _uiState.value

        // Basic validation
        if (currentState.email.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Email cannot be empty")
            return
        }

        if (currentState.password.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Password cannot be empty")
            return
        }

        if (currentState.password.length < 6) {
            _uiState.value = currentState.copy(errorMessage = "Password must be at least 6 characters")
            return
        }

        _uiState.value = currentState.copy(isLoading = true, errorMessage = "")

        viewModelScope.launch {
            try {
                val result = firebaseAuth.createUserWithEmailAndPassword(
                    currentState.email,
                    currentState.password
                ).await()

                // Send email verification
                result.user?.sendEmailVerification()?.await()

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isSignedIn = false, // Don't sign in until email is verified
                    isEmailVerified = false
                )

                // Pass email to verification screen
                onSuccess(currentState.email)

            } catch (e: FirebaseAuthException) {
                val errorMessage = when (e.errorCode) {
                    "ERROR_INVALID_EMAIL" -> "The email address is badly formatted."
                    "ERROR_EMAIL_ALREADY_IN_USE" -> "The email address is already in use by another account."
                    "ERROR_WEAK_PASSWORD" -> "The password is too weak."
                    else -> e.message ?: "Sign up failed. Please try again."
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = errorMessage
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "An unexpected error occurred. Please try again."
                )
            }
        }
    }
    
    fun signIn(onSuccess: () -> Unit) {
        val currentState = _uiState.value

        // Basic validation
        if (currentState.email.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Email cannot be empty")
            return
        }

        if (currentState.password.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Password cannot be empty")
            return
        }

        _uiState.value = currentState.copy(isLoading = true, errorMessage = "")

        viewModelScope.launch {
            try {
                firebaseAuth.signInWithEmailAndPassword(
                    currentState.email,
                    currentState.password
                ).await()

                // Check if email is verified
                val currentUser = firebaseAuth.currentUser
                if (currentUser?.isEmailVerified == true) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isSignedIn = true,
                        isEmailVerified = true
                    )
                    onSuccess()
                } else {
                    // Sign out the user if email is not verified
                    firebaseAuth.signOut()
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isSignedIn = false,
                        isEmailVerified = false,
                        errorMessage = "Please verify your email before logging in."
                    )
                }

            } catch (e: FirebaseAuthException) {
                val errorMessage = when (e.errorCode) {
                    "ERROR_INVALID_EMAIL" -> "The email address is badly formatted."
                    "ERROR_USER_NOT_FOUND" -> "No account found with this email address."
                    "ERROR_WRONG_PASSWORD" -> "Wrong password. Please try again."
                    "ERROR_USER_DISABLED" -> "This account has been disabled."
                    else -> e.message ?: "Sign in failed. Please try again."
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = errorMessage
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "An unexpected error occurred. Please try again."
                )
            }
        }
    }
    
    fun signOut(onSuccess: () -> Unit) {
        firebaseAuth.signOut()
        _uiState.value = _uiState.value.copy(
            email = "",
            password = "",
            isSignedIn = false,
            errorMessage = ""
        )
        onSuccess()
    }
    
    fun sendPasswordResetEmail(email: String, onSuccess: () -> Unit, onError: (String) -> Unit) {
        if (email.isBlank()) {
            onError("Email cannot be empty")
            return
        }

        viewModelScope.launch {
            try {
                firebaseAuth.sendPasswordResetEmail(email).await()
                onSuccess()
            } catch (e: FirebaseAuthException) {
                val errorMessage = when (e.errorCode) {
                    "ERROR_INVALID_EMAIL" -> "The email address is badly formatted."
                    "ERROR_USER_NOT_FOUND" -> "No account found with this email address."
                    else -> e.message ?: "Failed to send reset email. Please try again."
                }
                onError(errorMessage)
            } catch (e: Exception) {
                onError("An unexpected error occurred. Please try again.")
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = "")
    }
}
